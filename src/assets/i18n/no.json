{"ConfirmedOrders.display": "Vis", "ConfirmedOrders.goToPage": "Gå til side", "ConfirmedOrders.itemName": "Bekreftede ordre", "ConfirmedOrders.orderList.action": "Handling", "ConfirmedOrders.orderList.createdAt": "Oppret<PERSON><PERSON> dato", "ConfirmedOrders.orderList.customer": "Kunde", "orders.orderList.customer.viewCustomer": "Se kunde", "ConfirmedOrders.orderList.orderID": "Ordre ID", "ConfirmedOrders.orderList.revenue": "Inntekt", "ConfirmedOrders.orderList.status": "Status", "orders.orderList.noData": "Ingen ordre funnet", "orders.orderList.total": "Total", "ConfirmedOrders.page": "Side", "ConfirmedOrders.searchField": "Søk...", "ConfirmedOrders.status": "Status", "HOUR-ABBREVIATION": "t", "MONTHS-SHORT.1": "Jan", "MONTHS-SHORT.10": "Okt", "MONTHS-SHORT.11": "Nov", "MONTHS-SHORT.12": "Des", "MONTHS-SHORT.2": "Feb", "MONTHS-SHORT.3": "Mar", "MONTHS-SHORT.4": "Apr", "MONTHS-SHORT.5": "<PERSON>", "MONTHS-SHORT.6": "Jun", "MONTHS-SHORT.7": "Jul", "MONTHS-SHORT.8": "Aug", "MONTHS-SHORT.9": "Sep", "MONTHS.1": "<PERSON><PERSON><PERSON>", "MONTHS.10": "Oktober", "MONTHS.11": "November", "MONTHS.12": "Desember", "MONTHS.2": "<PERSON><PERSON><PERSON>", "MONTHS.3": "Mars", "MONTHS.4": "April", "MONTHS.5": "<PERSON>", "MONTHS.6": "<PERSON><PERSON>", "MONTHS.7": "<PERSON><PERSON>", "MONTHS.8": "August", "MONTHS.9": "September", "WEEKDAYS-METRIC.0": "Mandag", "WEEKDAYS-METRIC.1": "Tirsdag", "WEEKDAYS-METRIC.2": "Onsdag", "WEEKDAYS-METRIC.3": "Torsdag", "WEEKDAYS-METRIC.4": "Fred<PERSON>", "WEEKDAYS-METRIC.5": "<PERSON><PERSON><PERSON><PERSON>", "WEEKDAYS-METRIC.6": "<PERSON><PERSON><PERSON>g", "WEEKDAYS-SHORT-METRIC.0": "Man", "WEEKDAYS-SHORT-METRIC.1": "Tir", "WEEKDAYS-SHORT-METRIC.2": "Ons", "WEEKDAYS-SHORT-METRIC.3": "Tor", "WEEKDAYS-SHORT-METRIC.4": "Fre", "WEEKDAYS-SHORT-METRIC.5": "<PERSON><PERSON><PERSON>", "WEEKDAYS-SHORT-METRIC.6": "<PERSON>ø<PERSON>", "WEEKDAYS-SHORT.0": "<PERSON>ø<PERSON>", "WEEKDAYS-SHORT.1": "Man", "WEEKDAYS-SHORT.2": "Tir", "WEEKDAYS-SHORT.3": "Ons", "WEEKDAYS-SHORT.4": "Tor", "WEEKDAYS-SHORT.5": "Fre", "WEEKDAYS-SHORT.6": "<PERSON><PERSON><PERSON>", "WEEKDAYS.0": "<PERSON><PERSON><PERSON>g", "WEEKDAYS.1": "Mandag", "WEEKDAYS.2": "Tirsdag", "WEEKDAYS.3": "Onsdag", "WEEKDAYS.4": "Torsdag", "WEEKDAYS.5": "Fred<PERSON>", "WEEKDAYS.6": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktiv", "DAY": "dag", "DAYS": "dager", "HOUR": "time", "HOURS": "timer", "MINUTE": "minutt", "MINUTES": "minutter", "addOrder.payment.title": "Betalingsmetode", "addOrder.address.missingAddressStage": "Tjenesten du har valgt inneholder ikke noen steg med adresse. Vennligst verifiser stegene til produktet ditt.", "addOrder.address.selectSection": "<PERSON><PERSON><PERSON>", "addOrder.address.fromCustomer": "(Sist brukt for denne kunden)", "addOrder.address.tempAddressName": "<PERSON><PERSON><PERSON>", "addOrder.address.tempStageName": "ordre", "addOrder.assign.resources": "<PERSON><PERSON><PERSON><PERSON>", "addOrder.assign.resourcesPlaceholder": "<PERSON><PERSON><PERSON> ressurser", "addOrder.assign.users": "Ansatte", "addOrder.assign.user": "<PERSON><PERSON><PERSON>", "addOrder.assign.teams": "Ansatte", "addOrder.assign.usersPlaceholder": "<PERSON><PERSON>g ansatte", "addOrder.createOrderButton": "<PERSON><PERSON><PERSON><PERSON> ordre", "addOrder.createScheduleButton": "Opprett serie", "addOrder.notes.title": "Notater", "addOrder.customer.addPartnerButton": " <PERSON>gg til partner", "addOrder.customer.addPartnerTitle": "Partner", "addOrder.customer.brregSearchLabel": "Søk i Brønnøysundregisteret", "addOrder.customer.brregSearchLabelPlaceholder": "Søk på selskapsnavn eller organisasjonsnummer...", "addOrder.customer.businessCustomerAddress": "Selskapsadresse", "addOrder.customer.businessCustomerAddressSearchPlaceholder": "<PERSON><PERSON><PERSON> etter selskapsadresse", "addOrder.customer.businessToggle.private": "Privatkunde", "addOrder.customer.businessToggle.business": "Bedriftskunde", "addOrder.customer.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addOrder.customer.companyName": "Selskapsnavn", "addOrder.customer.noName": "Ingen navn", "addOrder.customer.createCustomerButton": "Op<PERSON>rett kunde", "addOrder.customer.createCustomerTitle": "Opprett ny kunde", "addOrder.customer.createNewCustomerButton": "Op<PERSON>rett kunde", "addOrder.customer.customerSearchPlaceholder": "<PERSON><PERSON><PERSON> etter kunde", "addOrder.customer.email": "E-post", "addOrder.customer.noEmail": "Ingen E-post", "addOrder.customer.emailError": "Ugyldig e-post", "addOrder.customer.firstName": "Fornavn", "addOrder.customer.firstNameError": "Ugyldig fornavn.", "addOrder.customer.invalidCompanyName": "Ugyldig selskapsnavn", "addOrder.customer.invalidEmail": "Ugylding e-post", "addOrder.customer.invalidOrgNumber": "Ugyldig organisasjonsnummer", "addOrder.customer.invoiceRecipientLabel": "<PERSON><PERSON><PERSON>mott<PERSON>", "addOrder.customer.lastName": "Etternavn", "addOrder.customer.lastNameError": "Ugyldig etternavn.", "addOrder.customer.noMatchesFound": "Ingen resultater funnet", "addOrder.customer.orgNumber": "Organisasjonsnummer", "addOrder.customer.phone": "Telefon", "addOrder.customer.noPhone": "Ingen telefonnummer", "addOrder.customer.phoneError": "Ugyldig telefonnummer.", "addOrder.customer.phoneErrorLength": "Telefonnummer er for kort", "addOrder.customer.searchPartnerPlaceholder": "Søk etter partners navn, eller partnerselskapets navn", "addOrder.customer.searching": "<PERSON><PERSON><PERSON>...", "addOrder.customer.title": "Kunde", "addOrder.customer.paymentRecipient": "Betalingsmottaker", "addOrder.customer.serviceRecipient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addOrder.customer.serviceRecipient.searchPlaceholder": "<PERSON><PERSON><PERSON> etter t<PERSON>", "addOrder.customer.new.title": "Op<PERSON>rett kunde", "addOrder.customer.useAsServiceRecipient": "Bruk som tjenestemottaker", "addOrder.missingFields.address": "n adresse for alle adressefelter", "addOrder.missingFields.customer": "n kunde", "addOrder.missingFields.duration_hours": "n varighet", "addOrder.missingFields.duration_hours_range": "t var<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addOrder.missingFields.execution date": "n utførelsesdato", "addOrder.missingFields.service": "n tjeneste", "addOrder.missingFields.time": "t gyldig tidspunkt", "addOrder.missingFields.title1": "Vennligst velg e", "addOrder.missingFields.title2": "før du oppretter ordren", "addOrder.preview.noProducts": "Tjenesteprodukt ikke valgt", "addOrder.preview.priceExVat": "Pris eks. MVA", "addOrder.preview.priceIncVat": "Totalt", "addOrder.preview.product": "Produkt", "addOrder.preview.productTotal": "Total", "addOrder.preview.quantity": "<PERSON><PERSON><PERSON>", "addOrder.preview.subtotal": "Subtotal", "addOrder.preview.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addOrder.preview.unitTypeHoursTooltip": "Antallet timer er et estimat, faktisk antall timer bestemmes av tiden som registreres på ordren", "addOrder.preview.vat": "MVA", "addOrder.preview.estimatedWork": "Estimert arbeidstid", "addOrder.preview.estimatedTransport": "Estimert transporttid", "addOrder.schedule.date": "Da<PERSON>", "addOrder.schedule.duration": "<PERSON><PERSON><PERSON><PERSON> varighet", "addOrder.schedule.durationRange": "<PERSON><PERSON><PERSON><PERSON> varighet", "addOrder.schedule.durationValidationError": "Varighet fra kan ikke være høyere enn varighet til", "addOrder.schedule.hourAbbr": "t", "addOrder.schedule.range": "Intervall", "addOrder.schedule.arrivalWindow": "Ankomstvindu", "addOrder.schedule.arrivalWindow.from": "<PERSON>a", "addOrder.schedule.arrivalWindow.to": "Til", "addOrder.schedule.error.setBothTimes": "<PERSON>t '<PERSON><PERSON>' og 'Til' tidspunkt.", "addOrder.schedule.error.arrivalFromBeforeArrivalTo": "'Fra' tidspunktet kan ikke være etter 'Til' tidspunkt.", "addOrder.schedule.multiDay": "Flerdagsordre", "addOrder.schedule.autoEstimate": "Auto-estimat", "addOrder.schedule.time": "Starttidspunkt", "addOrder.schedule.endDate": "Sluttdato", "addOrder.schedule.endTime": "Sluttidspunkt", "addOrder.schedule.to": "til", "addOrder.service.addAdditionalProductButton": "<PERSON>gg til ekstra produkt", "addOrder.service.additionalProducts": "Ekstra produkter", "addOrder.service.additionalProductsSearchPlaceholder": "S<PERSON>k etter ekstra produkter", "addOrder.service.lockedQuantityHoursTooltip": "Antal<PERSON> timer er låst til ordrens varighet. Antal<PERSON> vil settes til 1 når ordren opprettes.", "addOrder.service.noManualPriceRule": "Ingen manuelle prisregler tilgjengelig for dette produktet.", "addOrder.service.priceRules": "<PERSON><PERSON><PERSON><PERSON>", "addOrder.service.quantity": "<PERSON><PERSON><PERSON>", "addOrder.service.searchPlaceholder": "<PERSON><PERSON><PERSON> etter tjenesteprodukt", "addOrder.service.products.searchPlaceholder": "<PERSON><PERSON><PERSON> etter produkt", "addOrder.service.products.addCustomProduct": "Legg til egendefinert produkt", "addOrder.service.products.customProductUnitAbbreviation": "stk", "addOrder.service.selectPriceRules": "<PERSON><PERSON><PERSON>", "addOrder.service.title": "Tjeneste", "addOrder.title": "<PERSON><PERSON><PERSON><PERSON> ordre", "addOrder.title.schedule": "Opprett ordreserie", "addOrder.addInvoiceReference.button": "Legg til fakturareferanse", "addOrder.addInvoiceReference.title": "Fakturareferanse", "addOrder.addInvoiceReference.placeholder": "Skriv inn referanse", "addOrder.comment.title": "Fakturakommentar", "addOrder.comment.button": "Legg til fakturakommentar", "affiliates.itemName": "Bedrifter", "affiliates.details.backButton.private": "<PERSON><PERSON>", "affiliates.itemName.private": "Private kunder", "affiliates.addNew.private": "Legg til kunde", "affiliates.list.name.private": "Navn", "affiliates.list.email": "E-post", "affiliates.list.phone": "Telefon", "affiliates.addNew": "Legg til bedrift", "affiliates.importCustomers": "Importer kunder", "affiliates.list.name": "Bedriftsnavn", "affiliates.list.orgNumber": "Organisasjonsnummer", "affiliates.list.subContractor": "Underleverandør", "affiliates.list.customer": "Kunde", "affiliates.list.partner": "Partner", "affiliates.list.partnersOnly": "<PERSON><PERSON> <PERSON><PERSON>", "affiliates.list.customersOnly": "<PERSON>n kunder", "affiliates.list.subContractorsOnly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "affiliates.details.backButton": "Bedrifter", "affiliates.details.edit": "<PERSON><PERSON>", "affiliates.details.orders": "Ordre", "affiliates.details.prices": "<PERSON><PERSON><PERSON>", "affiliates.details.totalAmountIncVat": "Totalt inkl. MVA", "affiliates.details.contacts": "Bedriftskontakter", "affiliates.details.contacts.add": "Legg til kontakt", "affiliates.details.products.searchPlaceholder": "<PERSON><PERSON><PERSON> etter et produkt for å sette egne priser for bedriften", "affiliates.details.products.searchPlaceholder.private": "<PERSON><PERSON><PERSON> etter et produkt for å sette egne priser for kunden", "affiliates.details.generalSettings.title": "Innstillinger", "affiliates.details.generalSettings.hidePrices": "<PERSON><PERSON><PERSON><PERSON> priser for tjenestemottaker", "affiliates.details.generalSettings.disableSms": "Skru av SMS for denne kunden", "affiliates.details.generalSettings.disableSms.tooltip": "Aktiver for å skru av alle automatiske ordre-tekstmeldinger for denne kunden.", "affiliates.details.generalSettings.disableEmail": "Skru av e-post for denne kunden", "affiliates.details.generalSettings.disableEmail.tooltip": "Aktiver for å skru av alle automatiske ordre-eposter for denne kunden.", "affiliates.details.generalSettings.hidePrices.tooltip": "<PERSON><PERSON><PERSON> den<PERSON> for å skjule priser og ordretotaler for tjenestemottakere på ordre hvor denne bedriften er partner <PERSON><PERSON> beta<PERSON>.", "affiliates.details.generalSettings.useAsCustomer": "Bruk som kunde", "affiliates.details.generalSettings.useAsCustomer.tooltip": "Ved å merke bedriften som kunde vil bedriften og bedriftens kontakter vises i kundesøket når du oppretter ordre.", "affiliates.details.generalSettings.useAsPartner": "Bruk som partner", "affiliates.details.generalSettings.useAsPartner.tooltip": "<PERSON>ed <PERSON> merke bedriften som partner kan du legge ved bedriften som partner på ordre du oppretter. På en partnerordre vil partnerens priser <PERSON>, uavhengig hvem som settes som betalingsmottaker.", "affiliates.details.generalSettings.useAsSubContractor": "Bruk som underleverandør", "affiliates.details.generalSettings.useAsSubContractor.tooltip": "Ved å merke bedriften som underleverandør kan du sende ordre til underleverandøren som igjen vil kunne tildele ordren til sine ansatte. Betaling av ordre vil fortsatt skje til ditt selskap.", "affiliates.details.subContractorSettings.title": "Innstillinger for underleverandør", "affiliates.details.subContractorSettings.hidePrices": "<PERSON><PERSON><PERSON><PERSON> priser for underleverandør", "affiliates.details.subContractorSettings.hidePrices.tooltip": "<PERSON><PERSON><PERSON> denne for å skjule alle priser som kommer fra ditt selskap for denne underleverandøren.", "affiliates.details.subContractorSettings.fetchProducts": "Underleverandør kan hente mine produkter", "affiliates.details.subContractorSettings.fetchProducts.tooltip": "<PERSON><PERSON> denne er aktivert vil underleverandøren kunne hente inn dine produkter i ansatt-appen for å opprette nye ordrelinjer ved behov. Dette vil kun være mulig på jobber hvor underleverandøren er tildelt.", "affiliates.details.invoiceDetails.title": "Fakturadetaljer", "affiliates.details.invoiceDetails.invoiceEmail": "Faktura-epost", "affiliates.details.invoiceDetails.invoiceDueDate": "Forfallsdato", "affiliates.details.invoiceDetails.invoiceDueDatePostFix": "dager", "affiliates.details.invoiceDetails.sendType": "Fakturatype", "affiliates.details.invoiceDetails.consolidation": "Samlefaktura", "affiliates.details.invoiceConsolidation.title": "Samlefakt<PERSON><PERSON>", "affiliates.details.invoiceConsolidation.sendSelected": "Send valgte fakturaer", "affiliates.details.consolidatedInvoicePayments.title": "Betalinger som skal legges til samlefaktura", "affiliates.details.consolidatedInvoicePayments.consolidateButton": "Send nå med valgte betalinger", "affiliates.details.addresses.title": "<PERSON><PERSON><PERSON>", "affiliates.details.addresses.add": "Legg til adresse", "affiliates.details.notes.title": "Notater", "affiliates.details.notes.copyToOrder": "Vis i ordre", "affiliates.details.notes.copyToOrder.tootltip": "<PERSON><PERSON> denne er aktiv vil notatet kopieres til ordre hvor denne kontakten legges til som kunde. Sletting av notatet i ordren vil ikke påvirke dette notatet.", "affiliates.prices.modal.header": "Legg opp egendefinerte priser", "affiliates.prices.modal.prices": "Egendefinerte priser for ", "affiliates.prices.modal.subtext": "Her kan du definere egene priser for denne bedriften. <PERSON><PERSON> gjelder både hvis bedriften er kunde eller denne bedriften er partner", "affiliates.prices.modal.newBasePrice": "Sett ny basepris", "affiliates.prices.modal.productVariants": "Produktvarianter", "affiliates.prices.modal.noProductVariants": "Det er ingen produktvarianter for dette produktet", "affiliates.prices.modal.automaticPriceRules": "Automatiske prisregler", "affiliates.prices.modal.noAutomaticPriceRules": "Det er ingen prisregler for dette produktet", "affiliates.import.title": "Importer kunder fra regnskap", "affiliates.import.button": "Importer valgte", "addressSearch.label": "", "applicationUpdateMessage": "Vi har gjort noen oppdateringer. Trykk 'ok' for å oppdatere.", "applicationUnrecoverableState": "Applikasjonen er i en uopprettelig tilstand og må lastes på nytt. Trykk 'OK' for å fortsette.", "applicationUpdateTitle": "Opp<PERSON><PERSON> tilg<PERSON>g", "applicationUpdateNow": "Oppdater nå", "applicationUpdateLater": "<PERSON><PERSON>", "applicationUpdateCountdown": "Automatisk oppdatering om {{seconds}} sekunder", "apps.accounting": "Regnskap", "apps.all": "Alle", "apps.deleteBtn": "Avinstaller app", "apps.deleteModal.cancel": "Lukk", "apps.deleteModal.deleteItem": "Er du sikker på at du vil legge til denne integrasjonen? Ved å fortsette med installasjonen vil tidligere regnskapsintegrasjoner bli erstattet", "apps.deleteModal.install": "Installer", "apps.editApp": "<PERSON><PERSON>", "apps.noAppsSelected": "Ingen apper er valgt", "apps.instApp": "Installer", "apps.installBtn": "Installer", "apps.itemName": "Apper", "apps.myApps": "Mine apper", "apps.powGo": "Integrasjonen passer for alle som ønsker å overføre kunder, ordre og refusjoner til Power Office Go", "apps.tripletex": "Integrasjonen passer for alle som ønsker å overføre kunder, order og refusjoner til Tripletex", "apps.zapier": "Denne integrasjonen er ideell for å forbedre datamaskinering og operative prosesser", "areYouSure": "<PERSON>r du sikker?", "calendar.daily": "<PERSON><PERSON>", "calendar.monthly": "<PERSON><PERSON><PERSON>", "calendar.today": "I dag", "calendar.resources": "<PERSON><PERSON><PERSON><PERSON>", "calendar.employees": "Ansatte", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelModal.abortBtn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelModal.bodytext": "Er du sikker på at du vil avbryte denne bestillingen? Denne handlingen kan ikke angres", "cancelModal.cancelBtn": "Kanseller order", "cancelModal.header": "<PERSON>r du sikker?", "cancelModal.notify": "Send varsel per e-post til kunden", "chat-widget.agentLabel": "Kundesenter", "chat-widget.autoResponseMessage": "Hei, hva kan jeg hjelpe deg med?", "chat-widget.browserSideAuthorLabel": "", "chat-widget.collapsedMode": "chip", "chat-widget.conversationEndConfirmationQuestion": "Er du sikker på at du vil avslutte denne chatten?", "chat-widget.conversationEndLabel": "Chatten din er avsluttet. Takk for chatten!", "chat-widget.conversationEndMenuLinkLabel": "Avslutt chat", "chat-widget.conversationEndTranscriptPlaceholder": "E-post for å sende chattranskript", "chat-widget.conversationRatingLabel": "<PERSON><PERSON><PERSON> vil du vurdere denne chatten?", "chat-widget.conversationRatingPlaceholder": "Hvordan kan vi forbedre oss?", "chat-widget.conversationRatingThankYou": "Takk for at du vurderte chatten!", "chat-widget.conversationTranscriptSentThankYou": "Takk! Du vil motta chattranskriptet ditt om kort tid.", "chat-widget.defaultCancelBtnLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chat-widget.defaultNoBtnLabel": "<PERSON><PERSON>", "chat-widget.defaultOkBtnLabel": "Ok", "chat-widget.defaultSendBtnLabel": "Send", "chat-widget.defaultYesBtnLabel": "<PERSON>a", "chat-widget.noAnswerWithEmail": "Oops! Be<PERSON>ger at ingen har svart ennå. Vi har e-posten din i systemet om du må gå, eller du kan fortsette å vente.", "chat-widget.noAnswerWithoutEmail": "Oops! Be<PERSON>ger at ingen har svart ennå. Vi har e-posten din i systemet om du må gå, eller du kan fortsette å vente.", "chat-widget.offlineEmailPlaceholder": "E-post", "chat-widget.offlineGreeting": "<PERSON><PERSON><PERSON>, vi er borte, men vi vil gjerne høre fra deg og chatte snart!", "chat-widget.offlineMessagePlaceholder": "<PERSON> melding her", "chat-widget.offlineNamePlaceholder": "Navn (valgfritt men nyttig)", "chat-widget.offlineSendButton": "Send", "chat-widget.offlineThankYouMessage": "Takk for meldingen din. Vi tar kontakt snart!", "chat-widget.offlineTitle": "Kontakt oss", "chat-widget.onlineMessagePlaceholder": "<PERSON><PERSON><PERSON>v melding her...", "chat-widget.onlineTitle": "Hvordan kan vi hjelpe deg?", "chat-widget.requestScreenshotAllowLabel": "<PERSON> skjermbilde", "chat-widget.requestScreenshotDeclineLabel": "Avslå", "chat-widget.requestScreenshotText": "Operatøren vil ta et skjermbilde av nettleseren din. Bekreft nedenfor.", "chat-widget.requireInfoGreeting": "Skriv inn ditt navn og e-post for å starte chatten!", "chat-widget.requireInfoSubmitBtn": "Start", "common.at": "kl", "common.add": "Legg til", "common.backToSettings": "Tilbake til Innstillinger", "common.create": "<PERSON><PERSON><PERSON><PERSON>", "common.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "common.close": "Lukk", "common.save": "Lagre", "common.delete": "<PERSON><PERSON>", "common.remove": "<PERSON><PERSON><PERSON>", "common.continue": "Fortsett", "common.name": "Navn", "common.companyName": "<PERSON><PERSON><PERSON><PERSON>", "common.orgNumber": "Organisasjonsnummer", "common.firstName": "Fornavn", "common.lastName": "Etternavn", "common.email": "E-post", "common.sms": "SMS", "common.phone": "Telefon", "common.address": "<PERSON><PERSON><PERSON>", "common.edit": "<PERSON><PERSON>", "common.redigate": "<PERSON><PERSON>", "common.change": "<PERSON><PERSON>", "common.send": "Send", "common.active": "Aktiv", "common.inactive": "Inaktiv", "common.invoice": "Faktura", "common.noAddress": "Ingen adresse valgt", "common.days": "dager", "common.noPhone": "Mangler telefonnummer", "common.noEmail": "Mangler e-post", "common.loading": "Henter data", "common.noData": "Ingen data", "common.and": "og", "common.select": "Velg", "common.to": "til", "common.open": "<PERSON><PERSON><PERSON>", "common.on": "på", "common.noResults": "Ingen resultater funnet", "common.refund": "Refusjon", "common.by": "av", "common.vat": "MVA", "common.yes": "<PERSON>a", "common.no": "<PERSON><PERSON>", "common.date": "Da<PERSON>", "common.duration": "<PERSON><PERSON><PERSON><PERSON>", "common.settings": "Innstillinger", "common.ok": "OK", "common.total": "Totalt", "common.download": "Last ned", "common.deleted": "<PERSON><PERSON><PERSON>", "components.addressSearch.customAddress.title": "<PERSON><PERSON><PERSON><PERSON> adresse", "components.addressSearch.addressSearchPlaceholder": "<PERSON><PERSON><PERSON> etter adresse", "components.addressSearch.unitSearchPlaceholder": "<PERSON><PERSON><PERSON> (valgfritt)", "components.addressSearch.createNew": "Finner du ikke adressen? Op<PERSON>rett ny adresse", "components.addressSearch.customAddress.street": "Gatenavn", "components.addressSearch.customAddress.number": "<PERSON><PERSON><PERSON>", "components.addressSearch.customAddress.letter": "Bokstav", "components.addressSearch.customAddress.postalCode": "Postnummer", "components.addressSearch.customAddress.city": "Poststed", "components.addressSearch.customAddress.propertyType": "Boligtype", "companySearch.label": "Søk Brønnøysundregisteret", "companySearch.placeholder": "<PERSON><PERSON><PERSON> etter selskapsnavn eller orgnummer...", "createEvent.addressLabel": "<PERSON><PERSON><PERSON>", "createEvent.addressPlaceholder": "<PERSON><PERSON><PERSON> etter adresse", "createEvent.endDateLabel": "Slutttidspunkt", "createEvent.errorMessageEndTime": "Sluttidspunkt må være etter starttidspunkt", "createEvent.headerEvent": "<PERSON><PERSON><PERSON><PERSON> ny hendelse", "createEvent.headerOrder": "Lag ordre", "createEvent.selectEventAllDayLabel": "<PERSON><PERSON> dagen", "createEvent.selectEventDescriptionLabel": "Beskrivelse", "createEvent.selectEventDescriptionPlaceholder": "Beskrivelse av hendelsen", "createEvent.selectEventNameLabel": "Navn", "createEvent.selectEventNamePlaceholder": "Navn på hendelse", "createEvent.selectEventPlaceholder": "Hendelses type", "createEvent.selectEventResourcesPlaceholder": "<PERSON><PERSON> til ressurser", "createEvent.selectEventTypeLabel": "Aktivitetstype", "createEvent.selectEventUsersPlaceholder": "<PERSON><PERSON>g ansatte", "createEvent.selectedEventResourcesLabel": "<PERSON><PERSON><PERSON><PERSON>", "createEvent.selectedEventUsersLabel": "Ansatte", "createEvent.startDateLabel": "Starttidspunkt", "createEvent.tabs.createOrder": "Ordre", "createEvent.tabs.createOther": "<PERSON><PERSON>", "createEvent.tabs.connectToOrder": "Koble til ordre", "createEvent.tabs.createEmployee": "<PERSON><PERSON><PERSON>", "createEvent.btn.createEvent": "<PERSON><PERSON><PERSON><PERSON>", "createEvent.btn.editEvent": "<PERSON><PERSON>", "conflictModal.header": "Konflikt", "conflictModal.subHeader": "Den ansatte er tildelt følgende jobber som krysser med denne jobben:", "conflictModal.subHeader.absence": "Den ansatte har følgende fravær som krysser med denne jobben:", "conflictModal.btn.goBack": "<PERSON><PERSON> til<PERSON>", "conflictModal.btn.continue": "Fortsett likevel", "conflictModal.typeLabel": "Type:", "conflictModal.timeLabel": "Tid:", "searchEventsModal.header": "<PERSON><PERSON><PERSON> etter ordre", "searchEventsModal.noParamsSet": "Minst et søkefelt må fylles ut", "searchEventsModal.noResultFound": "Ingen resultater funnet", "searchEventModal.form.orderIdLabel": "Ordrenummer", "searchEventModal.form.orderIdPlaceholder": "Søk på ordrenummer", "searchEventModal.form.productLabel": "Produktnavn", "searchEventModal.form.productPlaceholder": "Søk på produkt", "searchEventModal.form.addressLabel": "<PERSON><PERSON><PERSON>", "searchEventModal.form.addressPlaceholder": "<PERSON><PERSON><PERSON> på adresse", "searchEventModal.form.customerLabel": "Kunde", "searchEventModal.form.commentLabel": "Fakturakommentar", "searchEventModal.form.emailLabel": "Kundens e-post", "searchEventModal.form.customerPlaceholder": "Søk på navn", "searchEventModal.form.commentPlaceholder": "<PERSON><PERSON><PERSON> etter fakturakommentar", "searchEventModal.form.customerPhoneLabel": "<PERSON><PERSON><PERSON> telefonn<PERSON>", "searchEventModal.form.customerPhonePlaceholder": "Søk på telefonnummer", "searchEventsModal.btn.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchEventsModal.btn.search": "<PERSON><PERSON><PERSON>", "customers.businessCustomers.accountingId": "Regnskaps-ID", "customers.businessCustomers.active": "Aktiv", "customers.businessCustomers.addNewCustomer": "Legg til kunde", "customers.businessCustomers.address": "Selskapsadresse", "customers.businessCustomers.addContact": "Legg til bedriftskontakt", "customers.businessCustomers.contact": "Bedriftskontakt", "customers.businessCustomers.city": "By", "customers.businessCustomers.customerId": "Kunde ID", "customers.businessCustomers.email": "E-post", "customers.businessCustomers.invoiceEmail": "E-post for faktura", "customers.businessCustomers.invoiceSendType": "Fakturatype", "customers.businessCustomers.invoiceDueDate": "Forfallsdato", "customers.businessCustomers.invoiceDueDatePostFix": "dager", "customers.businessCustomers.invoiceConsolidation": "Samlefaktura", "customers.businessCustomers.firstName": "Fornavn", "customers.businessCustomers.inactive": "Inaktiv", "customers.businessCustomers.invalidAccountId": "Ugyldig kontonummer", "customers.businessCustomers.invalidAddress": "Ugy<PERSON><PERSON> adresse", "customers.businessCustomers.invalidCity": "<PERSON><PERSON><PERSON><PERSON> by", "customers.businessCustomers.invalidCompanyName": "Ugyldig firmanavn", "customers.businessCustomers.invalidEmail": "Ugyldig e-postadresse", "customers.businessCustomers.invalidFirstName": "Ugyldig fornavn", "customers.businessCustomers.invalidLastName": "Ugyldig etternavn", "customers.businessCustomers.invalidOrganisationNumber": "Ugyldig organisasjonsnummer", "customers.businessCustomers.invalidPhone": "Ugyldig telefonnummer", "customers.businessCustomers.invalidPostalCode": "Ugyldig postnummer", "customers.businessCustomers.itemName": "Bedriftskunder", "customers.businessCustomers.lastName": "Etternavn", "customers.businessCustomers.name": "Bedriftsnavn", "customers.businessCustomers.new.create": "<PERSON><PERSON><PERSON><PERSON>", "customers.businessCustomers.new.editPageName": "Rediger bedriftskunde", "customers.businessCustomers.new.newPageName": "Ny bedriftskunde", "customers.businessCustomers.new.save": "Lagre", "customers.businessCustomers.numberOfOrders": "<PERSON><PERSON><PERSON> ordre", "customers.businessCustomers.organisationNumber": "Organisasjonsnummer", "customers.businessCustomers.phone": "Telefon", "customers.businessCustomers.postalCode": "Postnummer", "customers.businessCustomers.revenue": "Omsetning", "customers.businessCustomers.searchField": "Søk...", "customers.businessCustomers.otherActions": "Flere valg", "customers.businessCustomers.editInfo": "Rediger kunde", "customers.businessCustomers.newConsolidatedInvoice": "Opprett nytt samlefakturaoppsett", "customers.businessCustomers.editAccountingInfo": "Rediger regnskapsinformasjon", "customers.businessCustomers.editInvoiceDetails": "<PERSON>iger fakturadetaljer", "customers.businessCustomers.invoiceDetails": "Fakturadetaljer", "customers.orderList.address": "<PERSON><PERSON><PERSON>", "customers.orderList.orderDate": "Opprettelsesdato", "customers.orderList.jobDate": "<PERSON><PERSON><PERSON><PERSON>", "customers.orderList.orderId": "Ordrenummer", "customers.orderList.revenue": "Inntekt", "customers.orderList.status": "Status", "customers.orderList.type": "Type", "customers.privateCustomers.accountingId": "Regnskaps-ID", "customers.privateCustomers.active": "Aktiv", "customers.privateCustomers.addNewCustomer": "Legg til ny kunde", "customers.privateCustomers.address": "<PERSON><PERSON><PERSON>", "customers.privateCustomers.city": "By", "customers.privateCustomers.contactName": "Navn", "customers.privateCustomers.contactPerson": "Kontaktperson", "customers.privateCustomers.customerId": "Kunde-ID", "customers.privateCustomers.email": "E-post", "customers.privateCustomers.firstName": "Fornavn", "customers.privateCustomers.notEditable": "Brukeren tilknyttet denne kunden er registrert som kunde hos flere selskaper. Personopplysninger for denne kunden kan derfor ikke endres.", "customers.privateCustomers.inactive": "Inaktiv", "customers.privateCustomers.invalidAccountId": "Ugyldig kontonummer", "customers.privateCustomers.invalidAddress": "Ugy<PERSON><PERSON> adresse", "customers.privateCustomers.invalidCity": "<PERSON><PERSON><PERSON><PERSON> by", "customers.privateCustomers.invalidCompanyName": "Ugyldig firmanavn", "customers.privateCustomers.invalidEmail": "Ugyldig e-postadresse", "customers.privateCustomers.invalidFirstName": "Ugyldig fornavn", "customers.privateCustomers.invalidLastName": "Ugyldig etternavn", "customers.privateCustomers.invalidOrganisationNumber": "Ugyldig organisasjonsnummer", "customers.privateCustomers.invalidPhone": "Ugyldig telefonnummer", "customers.privateCustomers.invalidPostalCode": "Ugyldig postnummer", "customers.privateCustomers.itemName": "Private kunder", "customers.privateCustomers.lastName": "Etternavn", "customers.privateCustomers.name": "Navn", "customers.privateCustomers.accountingLabel": "Koble en kunde fra regnskap til denne kunden", "customers.privateCustomers.accountingPlaceholder": "<PERSON><PERSON><PERSON> etter kunde i regnskapssystemet ditt", "customers.privateCustomers.invoiceDueDate": "Forfallsdato for faktura", "customers.privateCustomers.new.create": "<PERSON><PERSON><PERSON><PERSON>", "customers.privateCustomers.new.editPageName": "Rediger privat kunde", "customers.privateCustomers.new.newPageName": "Ny privat kunde", "customers.privateCustomers.new.save": "Lagre", "customers.privateCustomers.numberOfOrders": "<PERSON><PERSON><PERSON> bestillinger", "customers.privateCustomers.organisationNumber": "Organisasjonsnummer", "customers.privateCustomers.phone": "Telefon", "customers.privateCustomers.postalCode": "Postnummer", "customers.privateCustomers.primaryAddress": "Primæradresse", "customers.privateCustomers.revenue": "Inntekt", "customers.privateCustomers.searchField": "Søk...", "customers.privateCustomers.createdAt": "<PERSON><PERSON><PERSON> opprettet:", "customers.privateCustomers.deals": "Tilbud", "customers.privateCustomers.bookedRevenue": "Booket inntekt", "customers.privateCustomers.jobs": "<PERSON><PERSON>", "customers.privateCustomers.job": "<PERSON><PERSON>", "customers.privateCustomers.allCustomers": "Private kunder", "dashboard.itemName": "Dashboard", "dashboard.widgets.weeklyRevenue": "Inntekt denne uken", "dashboard.widgets.last5orders": "Siste 5 ordre", "dashboard.widgets.monthlyRevenue": "", "dashboard.widgets.newOrdersThisMonth": "<PERSON><PERSON> ordre denne m<PERSON>en", "dashboard.widgets.newOrdersToday": "Nye ordre i dag", "dashboard.widgets.plannedJobsToday": "Planlagte jobber i dag", "dashboard.widgets.resourceCalendar": "<PERSON><PERSON><PERSON>", "dashboard.widgets.revenue.currentMonth": "Nåværende måned", "dashboard.widgets.revenue.itemName": "Inntekt", "dashboard.widgets.revenue.previousMonth": "<PERSON><PERSON><PERSON>", "dashboard.widgets.revenue.yAxisTitle": "Inntekt (NOK)", "date-range-picker.last30Days": "Siste 30 dager", "date-range-picker.last7Days": "Siste 7 dager", "date-range-picker.lastMonth": "<PERSON><PERSON><PERSON>", "date-range-picker.lastYear": "<PERSON><PERSON><PERSON>", "date-range-picker.monthToDate": "<PERSON><PERSON><PERSON> til dato", "date-range-picker.thisMonth": "<PERSON><PERSON>", "date-range-picker.thisYear": "<PERSON><PERSON>", "date-range-picker.to": "til", "date-range-picker.today": "I dag", "date-range-picker.yearToDate": "<PERSON><PERSON> til dato", "date-range-picker.yesterday": "<PERSON> går", "deleteBtn.bodytext": "Er du sikker på at du ønsker å slette?", "deleteBtn.closeBtn": "Lukk", "deleteBtn.deleteBtn": "<PERSON><PERSON>", "deleteBtn.header": "<PERSON><PERSON>", "editEvent.addressLabel": "<PERSON><PERSON><PERSON>", "editEvent.addressPlaceholder": "<PERSON><PERSON><PERSON> etter adresse", "editEvent.endDateLabel": "Slutttidspunkt", "editEvent.errorMessageEndTime": "Sluttidspunkt må være etter starttidspunkt", "editEvent.headerEvent": "<PERSON><PERSON>", "editEvent.headerOrder": "<PERSON><PERSON> or<PERSON>", "editEvent.save": "Lagre", "editEvent.selectEventAllDayLabel": "<PERSON><PERSON> dagen", "editEvent.selectEventDescriptionLabel": "Beskrivelse", "editEvent.selectEventDescriptionPlaceholder": "Beskrivelse av hendelsen", "editEvent.selectEventNameLabel": "Navn", "editEvent.selectEventNamePlaceholder": "Navn på hendelse", "editEvent.selectEventPlaceholder": "Hendelses type", "editEvent.selectEventResourcesPlaceholder": "<PERSON><PERSON> til ressurser", "editEvent.selectEventTypeLabel": "Lag hendelse", "editEvent.selectEventUsersPlaceholder": "<PERSON><PERSON>g ansatte", "editEvent.selectedEventResourcesLabel": "<PERSON><PERSON><PERSON><PERSON>", "editEvent.selectedEventUsersLabel": "Ansatte", "editEvent.startDateLabel": "Starttidspunkt", "employees.add-employee.title": "<PERSON><PERSON> til ansatt", "employees.edit-employee.title": "<PERSON><PERSON> ansatt", "employees.edit": "<PERSON><PERSON> Ansatt", "employees.addEmployee": "<PERSON><PERSON> til ansatt", "employees.import": "Importer", "employees.companyRole": "<PERSON><PERSON>", "employees.email": "E-post", "employees.employee-details.addProduct": "Legge til et produkt i ordre", "employees.employee-details.assignableForJobs": "Tilgjengelig for jobb-tildeling", "employees.employee-details.availability": "Tilgjengelighet", "employees.employee-details.clickHereToUploadImage": "<PERSON><PERSON><PERSON> her for å laste opp bilde", "employees.employee-details.coreAttributesTitle": "Ansattes innstillinger for Core", "employees.employee-details.editDiscount": "<PERSON><PERSON>", "employees.employee-details.editQuantity": "<PERSON><PERSON> antallet på et produkt", "employees.employee-details.email": "E-post", "employees.employee-details.accountingLabel": "Ko<PERSON> den ansatte til en ansatt i ditt regnskapssystem", "employees.employee-details.hourlyRatePlaceholder": "Oppgi standard timeslønn for den ansatte", "employees.employee-details.hourlyRate": "<PERSON><PERSON><PERSON><PERSON>", "employees.employee-details.employeeAttributesTitle": "I Crew Appen kan den ansatte:", "employees.employee-details.employeeInformation": "Ansattinformasjon", "employees.employee-details.equalPhone": "Nummeret er allerede i bruk", "employees.employee-details.firstName": "Fornavn", "employees.employee-details.generalInformation": "Generell informasjon", "employees.employee-details.invalidEmail": "Ugyldig e-post", "employees.employee-details.invalidFirstName": "Ugyldig fornavn", "employees.employee-details.invalidLastName": "Ugyldig etternavn", "employees.employee-details.invalidPhone": "Ugyldig telefonnummer", "employees.employee-details.invalidPhoneNumber": "Ugyldig telefonnummer", "employees.employee-details.invalidRole": "Ugyldig rolle", "employees.employee-details.lastName": "Etternavn", "employees.employee-details.logo": "Ansattbilde", "employees.employee-details.makeExternalNotes": "Lage eksterne notater", "employees.employee-details.makeExternalReports": "Lage eksterne rapporter", "employees.employee-details.notifications": "<PERSON><PERSON><PERSON>", "employees.employee-details.pageTitle": "<PERSON><PERSON><PERSON> instillinger", "employees.employee-details.accordion.generalInformation": "Generell informasjon", "employees.employee-details.paymentPageAccess": "Se betalings- og ordresammendrag", "employees.employee-details.phone": "Telefon", "employees.employee-details.properties": "Egenskaper", "employees.employee-details.receiveAccountingIntegrationFailedNotification": "<PERSON><PERSON> varsel på e-post ved regnskapsfeil", "employees.employee-details.receiveOrderAcceptedNotificationEmail": "<PERSON><PERSON> varsel på e-post når et tilbud aksepteres av kunden", "employees.employee-details.receiveOrderAcceptedNotificationSMS": "<PERSON><PERSON> varsel på SMS når et tilbud aksepteres av kunden", "employees.employee-details.receiveOrderFinishedNotificationSMS": "<PERSON><PERSON> var<PERSON> på SMS når en ordre fullføres", "employees.employee-details.receiveUnpaidOrderAlert": "<PERSON><PERSON> varsel på e-post ved utestående betaling", "employees.employee-details.receiveUnconfirmedOrderAlert": "<PERSON><PERSON> varsel på e-post om ikke-bekreftede kommende ordre", "employees.employee-details.receiveSubContractorOrderNotification": "<PERSON><PERSON> varsel på e-post om opddateringer på jobber for underleverandører", "employees.employee-details.receiveOrderRatingNotification": "<PERSON><PERSON> varsel på e-post om rating av ordre", "employees.employee-details.receiveEmbedOrderNotification": "<PERSON><PERSON> varsel på e-post om ny ordre fra bestillingsskjema", "employees.employee-details.receiveEmailErrorNotification": "<PERSON><PERSON> varsel på e-post ved feil i e-postutsendelse", "employees.employee-details.receiveCustomerMessageNotification": "<PERSON><PERSON> varsel på e-post når en kunde har sendt en melding", "employees.employee-details.receiveUnconfirmedOrderAlert.tooltip": "Systemet vil daglig sende deg et varsel på e-post om ordre som skal gjennomføres påfølgende dag, men som enda ikke er bekreftet av enten selskapet eller kunden", "employees.employee-details.receiveUnpaidOrderAlert.tooltip": "Systemet sender automatisk ut to påminnelser til kunden dersom ordren ikke blir betalt. Hvis betalingen fortsatt ikke er mottatt etter den andre på<PERSON>, vil du få en e-post som informerer om dette, når denne innstillingen er aktivert.", "employees.employee-details.recurringOrderCreationFailed": "<PERSON><PERSON> varsel på e-post ved feil i opprettelse av repeterende ordre", "employees.employee-details.resendInvitation": "Send ny invitasjon", "employees.employee-details.accountingId": "Regnskaps-ID", "employees.employee-details.notConnectedToAccounting": "Ikke koblet til regnskap", "employees.employee-details.role": "<PERSON><PERSON>", "employees.employee-details.role.admin.description": "<PERSON>r tilgang til både Crew-appen og Core", "employees.employee-details.role.crew.description": "Har kun tilgang til Crew-appen", "employees.employee-details.noSalariesCreated": "Ingen lønninger er opprettet ennå. Klikk på Opprett-knappen for å legge til en lønn.", "employees.employee-details.noVacationDaysCreated": "Ingen feriedager er opprettet ennå. Klikk på Opprett-knappen for å legge til feriedager.", "employees.employee-details.noAbsencesCreated": "Ingen fraværsperioder er opprettet ennå. Klikk på Opprett-knappen for å legge til en fraværsperiode.", "employees.employee-details.noInvitationRole": "Ansatte med denne rollen vil ikke motta invitasjonsmail", "employees.employee-details.selectRole": "Velg rolle", "employees.employee-details.showInCalendar": "<PERSON>is i kalender", "employees.employee-details.skipInvitation": "Opprett uten invitasjon", "employees.employee-details.skipInvitation.tooltip": "<PERSON>ligvis sendes det ut en invitasjon til den ansatte når de opprettes. Hvis du ikke ønsker å sende invitasjon nå, kan du gjøre dette senere.", "employees.employee-details.save": "Lagre", "employees.employee-details.sendToPayment": "Sende til betaling", "employees.employee-details.setPaymentToExternal": "Håndtere ordrebetaling eksternt", "employees.employee-details.crew-access.accessControl": "Tilgangskontroll for ansatt app", "employees.employee-details.crew-access.selectAllPermissions": "Velg alle tillate<PERSON>er", "employees.employee-details.crew-access.expandAll": "<PERSON><PERSON><PERSON> alle", "employees.employee-details.crew-access.compactAll": "Sk<PERSON><PERSON> alle", "employees.employee-details.crew-access.orders": "Ordre", "employees.employee-details.crew-access.changeDateAndTime": "<PERSON><PERSON> dato og tid", "employees.employee-details.crew-access.editDiscount": "<PERSON>gg til og endre rabatt", "employees.employee-details.crew-access.confirmOrder": "Bekreft ordre", "employees.employee-details.crew-access.sendToPayment": "Send til betaling", "employees.employee-details.crew-access.editOrderLines": "Legg til og rediger ordrelinjer", "employees.employee-details.crew-access.completeJob": "Fullf<PERSON>re ordren", "employees.employee-details.crew-access.viewPrices": "<PERSON> priser", "employees.employee-details.crew-access.viewOrderLines": "<PERSON> ordresa<PERSON>g", "employees.employee-details.crew-access.viewOrderLines.tooltip": "Gir ansatte tilgang til å se ordresammendrag med alle produkter og priser i ansatt-appen.", "employees.employee-details.crew-access.cancelOrder": "<PERSON><PERSON><PERSON><PERSON><PERSON> ordre", "employees.employee-details.crew-access.editEmployees": "<PERSON>gg til og endre ansatte", "employees.employee-details.crew-access.createOrder": "<PERSON><PERSON>rett ny ordre", "employees.employee-details.crew-access.geoLock": "Geo-l<PERSON>s sjekk-in", "employees.employee-details.crew-access.geoLockOut": "Geo-lås sjekk-ut", "employees.employee-details.crew-access.start_work_order_outside_execution_date": "Start arbeid på ordre utenfor utførelsesdato", "employees.employee-details.crew-access.calendar": "<PERSON><PERSON><PERSON>", "employees.employee-details.crew-access.viewAllOrdersAsCrew": "Se alle selskapets ordre", "employees.employee-details.crew-access.viewAllOrdersAsCrew.tooltip": "Normal kan ansatte kun se sine egene ordre, men ved å aktivere denne innstillingen vil de kunne se alle ordre i selskapet.", "employees.employee-details.crew-access.viewAllEventsAsCrew": "Se alle selskapets hendelser", "employees.employee-details.crew-access.viewAllEventsAsCrew.tooltip": "Normalt kan ansatte kun se sine egne hendelser, men ved å aktivere denne innstillingen vil de kunne se alle hendelser i selskapet.", "employees.employee-details.crew-access.createEvent": "<PERSON><PERSON><PERSON><PERSON> ny hendelse", "employees.employee-details.crew-access.editOwnEvent": "<PERSON>iger egne hendelse", "employees.employee-details.crew-access.editCompanyEvents": "Rediger alle selskapets hendelser", "employees.employee-details.crew-access.editCompanyEvents.tooltip": "Aktiver for å gi ansatte tilgang til å redigere alle hendelser i selskapet. Deaktiver for å kun gi tilgang til egne hendelser.", "employees.employee-details.crew-access.viewUnconfirmedOrders": "Se ubekreftede ordre", "employees.employee-details.crew-access.timetracking": "Timeføring", "employees.employee-details.crew-access.editTimeTracking": "<PERSON><PERSON> timeføringer", "employees.employee-details.crew-access.createManualTimeTracking": "Legg til manuelle tidsregistreringer", "employees.employee-details.crew-access.edit_resources": "<PERSON><PERSON> ressurser", "employees.employee-details.crew-access.add_payment_recipient": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "employees.employee-details.viewPrices": "<PERSON> priser", "employees.employee-details.accessControl": "Tilgangskontroll", "employees.employee-details.accessControlCrew": "Tilgangskontroll for ansatt app", "employees.employee-details.accessControl.viewAllEventsAsCrew": "Tilgang til alle hendelser i crew-kalenderen", "employees.employee-details.accessControl.viewAllEventsAsCrew.tooltip": "Aktiver for å gi tilgang alle hendelser, både tildelte og ikke-tildelte, i crew-kalenderen. Deaktiver for å kun gi tilgang til tildelte hendelser.", "employees.employee-details.accessControl.viewAllOrdersAsCrew": "Tilgang til alle ordre i crew-kalenderen", "employees.employee-details.accessControl.viewAllOrdersAsCrew.tooltip": "Aktiver for å gi tilgang til alle ordre, både tildelte og ikke-tildelte, i crew-kalenderen. Deaktiver for å kun gi tilgang til tildelte ordre.", "employees.employee-details.accessControl.viewPrices": "Vis priser i ansatt-app ", "employees.employee-details.accessControl.viewPrices.tooltip": "Gir ansatte tilgang til å se selskapets priser i ansatt-appen.", "employees.employee-details.accessControl.viewOrderLines": "Vis ordresammendrag i ansatt-app", "employees.employee-details.accessControl.viewOrderLines.tooltip": "Gir ansatte tilgang til å se ordresammendrag med alle produkter og priser i ansatt-appen.", "employees.employee-details.accessControl.sendToPayment": "Send til betaling i ansatt-app ", "employees.employee-details.accessControl.sendToPayment.tooltip": "Gir den ansatte tilgang til å sende en ordre til betaling.", "employees.employee-details.accessControl.geoLock": "Aktiver geo-lås for innsjekk", "employees.employee-details.accessControl.geoLock.tooltip": "Aktiver for å sikre at den ansatte ikke kan sjekke inn på en jobb uten å være innenfor en gitt avstand fra jobbadressen. Rekkevidden kan spesifiseres i selskapsinnstillinger.", "employees.employee-details.accessControl.geoLockOut.tooltip": "Aktiver for å sikre at den ansatte ikke kan sjekke ut av en jobb uten å være innenfor en gitt avstand fra jobbadressen. Rekkevidden kan spesifiseres i selskapsinnstillinger.", "employees.employee-details.accessControl.start_work_order_outside_execution_date.tooltip": "Aktiver for å tillate ansatte å starte arbeid på en ordre før utførelsesdatoen.", "employees.id": "ID", "employees.itemName": "Ansatte", "employees.name": "Navn", "employees.phone": "Telefon", "errors.email": "Vennligst skriv inn en gyldig e-postadresse", "errors.max": "Over høyeste tillatte verdi", "errors.maxlength": "Over maksimal tillatt lengde", "errors.min": "Under minste tillatte verdi", "errors.minlength": "Under minimum lengde", "errors.pattern": "Vennligst følg det angitte formatet", "errors.phone": "Vennligst skriv inn et gyldig telefonnummer", "errors.required": "<PERSON>te feltet er påkrevd", "eventDetails.ResourcesAndReports": "Ansatte og ressurser", "eventDetails.addressAndRoute": "Adresse og kjørerute", "eventDetails.checkLists": "Rapporter og sjekklister", "eventDetails.generalInformation": "Generell informasjon", "eventDetails.notesAndCQ": "Notater og spørreskjema", "forgotPassword.backToLogin": "Back to login", "forgotPassword.backToLoginLabel1": "Back to ", "forgotPassword.backToLoginLabel2": "<PERSON><PERSON>", "forgotPassword.description": "Enter your e-mail and we'll send you instructions to reset your password", "forgotPassword.emailLabel": "E-mail", "forgotPassword.emailPlaceholder": "Enter your e-mail", "forgotPassword.submitButtonLabel": "Submit", "forgotPassword.successMessage": "An email has been sent to you with instructions on how to reset your password", "forgotPassword.title": "Reset password", "forgotPassword.shortPasswordError": "Passordet må være minst 3 tegn ", "forgotPassword.shortPasswordErrorConfirm": "Passordet må være minst 3 tegn", "forgotPassword.validPhone": "", "hours": "timer", "icons.title": "Vennligst velg et ikon:", "inactive": "Inaktiv", "inventory.availableOutOfStock": "Tilgjengelig når utsolgt", "inventory.enterSku": "Skriv inn SKU", "inventory.enterStockQuantity": "Skriv inn nåværende lagerbeholdning", "inventory.enterWeight": "Skriv inn vekt", "inventory.sku": "SKU", "inventory.stockQuantity": "Lagerbeholdning", "inventory.weight": "Vekt", "justNow": "nå nylig", "labels.email": "E-post", "labels.firstName": "Fornavn", "labels.lastName": "Etternavn", "labels.phone": "Telefon", "leftSideNav.itemName": "<PERSON><PERSON>", "leftSideNav.navItem.apps.itemName": "Apper", "leftSideNav.navItem.support.itemName": "Ansatt-app", "leftSideNav.navItem.customers.businessCustomers": "Bedrift", "leftSideNav.navItem.customers.itemName": "Privat", "leftSideNav.navItem.businesses.itemName": "Bedrift", "leftSideNav.navItem.customers.privateCustomers": "Privat", "leftSideNav.navItem.dashboard.itemName": "Dashboard", "leftSideNav.navItem.employees.itemName": "Ansatte", "leftSideNav.navItem.orders.allOrders": "Alle ordre", "leftSideNav.navItem.orders.itemName": "Ordre", "leftSideNav.navItem.workOrders.itemName": "<PERSON><PERSON>", "leftSideNav.navItem.receivedWorkOrders.itemName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leftSideNav.navItem.orderSchedules.itemName": "Repeterende ordre", "leftSideNav.navItem.orders.orderConfirmation": "Ordrebekreftelse", "leftSideNav.navItem.partners.itemName": "<PERSON><PERSON>", "leftSideNav.navItem.partners.paperCompanies": "Papirselskaper", "leftSideNav.navItem.partners.partnerCompanies": "Partnerselskaper", "leftSideNav.navItem.partners.tabs.contact": "<PERSON><PERSON><PERSON><PERSON>", "leftSideNav.navItem.partners.tabs.pricing": "Prising", "leftSideNav.navItem.partners.tabs.settings": "<PERSON>st<PERSON><PERSON>", "leftSideNav.navItem.products.allProducts": "Alle produkter", "leftSideNav.navItem.products.itemName": "<PERSON>du<PERSON><PERSON>", "leftSideNav.navItem.products.productCategories": "Produktkategorier", "leftSideNav.navItem.reports.allReports": "Alle rapporter", "leftSideNav.navItem.reports.itemName": "Rapporter", "leftSideNav.navItem.reports.financialReports": "Finansiell", "leftSideNav.navItem.reports.employeeTimeTracking": "Timeføring", "leftSideNav.navItem.reports.employeeReports": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leftSideNav.navItem.reports.orderReports": "Ordrerapporter", "leftSideNav.navItem.resourceCalendar.itemName": "<PERSON><PERSON><PERSON>", "leftSideNav.navItem.resources.itemName": "<PERSON><PERSON><PERSON><PERSON>", "leftSideNav.navItem.payments.itemName": "<PERSON><PERSON>", "leftSideNav.navItem.salary.itemName": "Lønn og timesføring", "leftSideNav.navItem.salary.approval": "<PERSON><PERSON><PERSON><PERSON> timer", "leftSideNav.navItem.salary.approvedHours": "<PERSON><PERSON><PERSON><PERSON> timer", "leftSideNav.navItem.settings.accounting": "Regnskap", "leftSideNav.navItem.settings.company": "Selskap", "leftSideNav.navItem.settings.embed": "Bestillingsskjema", "leftSideNav.navItem.settings.salary": "<PERSON><PERSON><PERSON>", "leftSideNav.navItem.settings.calculations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leftSideNav.navItem.settings.integrations": "Integrasjoner", "leftSideNav.navItem.settings.itemName": "Innstillinger", "leftSideNav.navItem.contacts.itemName": "<PERSON><PERSON><PERSON><PERSON>", "leftSideNav.navItem.settings.notifications": "<PERSON><PERSON><PERSON>", "leftSideNav.navItem.settings.payment": "<PERSON><PERSON>", "leftSideNav.navItem.settings.billing": "Fakturering", "leftSideNav.navItem.settings.resources": "<PERSON><PERSON><PERSON><PERSON>", "leftSideNav.navItem.settings.calendar": "<PERSON><PERSON><PERSON>", "leftSideNav.navItem.superadmin.companies": "Selskaper", "leftSideNav.navItem.superadmin.itemName": "Superadministrator", "leftSideNav.navItem.templates.itemName": "Maler", "leftSideNav.navItem.create.itemName": "<PERSON><PERSON><PERSON><PERSON>", "leftSideNav.navItem.create.order": "Ordre", "leftSideNav.navItem.templates.customerQuestions": "Spørreskjema", "leftSideNav.navItem.templates.tasks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leftSideNav.navItem.templates.workOrders": "<PERSON><PERSON>", "leftSideNav.navItem.templates.importantInformation": "Viktig informasjon", "login.title": "Logg inn i Between", "login.description": "Skriv inn telefonnummer og passord for å logge inn i Between.", "login.forgotPasswordLabel": "Glemt passord?", "login.invalidPassword": "Ugyldig passord", "login.invalidPhone": "Ugyldig telefonnummer", "login.loginButtonLabel": "Logg inn", "login.loginFailedMessage": "Innlogging feilet: ", "login.loginFailedReason": "<PERSON>il telefonnummer eller passord", "login.passwordLabel": "Passord", "login.passwordLengthError": "Passordet må være minst 6 tegn langt", "login.passwordRequiredError": "Passord er påkrevd", "login.signUpLabel1": "Har du ikke en konto?", "login.signUpLabel2": "Registrer deg", "login.usernameIsRequired": "Telefonnummer er påkrevd", "login.usernameLabel": "Telefon", "minutes": "minutter", "no": "<PERSON><PERSON>", "addOrder.paymentOption.title": "<PERSON><PERSON><PERSON> vil du ta betalt?", "addOrder.paymentOption.fixedPrice": "Fast pris", "addOrder.paymentOption.perVisit": "<PERSON>", "orderSchedules.itemName": "Repeterende ordre", "orderSchedules.fixedPayment.date": "Månedlig faktura<PERSON>to", "orderSchedules.fixedPayment.every": "Hver", "orderSchedules.fixedPayment.description": "Faktura beskrivelse", "orderSchedules.fixedPayment.description.tooltip": "Beskrivelsen vil brukes som produktbeskrivelse på faktura.", "orderSchedules.fixedPayment.reference": "Faktura referanse", "orderSchedules.fixedPayment.month": "må<PERSON>", "orderSchedules.fixedPayment.vatRate": "MVA-sats", "orderSchedules.fixedPayment.totalPrice": "Fak<PERSON>belø<PERSON>", "orderSchedules.fixedPayment.title": "Fast betaling", "orderSchedules.fixedPayment.button": "<PERSON><PERSON><PERSON><PERSON> fast betaling", "orderSchedules.creationFailed": "Ordreoppret<PERSON><PERSON> feilet", "orderSchedules.createNewSchedule": "Lag ny serie", "orderSchedules.weekdays": "<PERSON><PERSON><PERSON>", "orderSchedules.monthWeekViewTooltip": "Klikk for ukesbasert månedlig plan", "orderSchedules.monthDateViewTooltip": "Klikk for datobasert månedlig plan", "orderSchedules.inAdvance": "Hvor mange jobber skal opprettes i forkant?", "orderSchedules.inAdvance.tooltip1": "Eksempel: En repeterende jobb som starter i dag vil opprette", "orderSchedules.inAdvance.tooltip2": "jobber. Når en av disse blir fullført eller utførelsesdatoen er passert, vil den neste jobben opprettes.", "orderSchedules.inAdvance.suffix": "jobber", "orderSchedules.repeat": "Hvor ofte?", "orderSchedules.every": "Hver", "orderSchedules.daily": "<PERSON><PERSON><PERSON>", "orderSchedules.weekly": "<PERSON>ken<PERSON><PERSON>", "orderSchedules.monthly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orderSchedules.day": "dag", "orderSchedules.week": "uke", "orderSchedules.month": "må<PERSON>", "orderSchedules.days": "dager", "orderSchedules.weeks": "uker", "orderSchedules.months": "<PERSON><PERSON><PERSON><PERSON>", "orderSchedules.descriptionLabel.workOrder": "<PERSON><PERSON> vil gjentas", "orderSchedules.descriptionLabel.payment": "Betalingen vil gjentas", "orderSchedules.descriptionLabel.consolidatedInvoice": "Samlefaktura vil sendes", "orderSchedules.ordersInAdvance": "<PERSON>tal<PERSON> ordre i <PERSON>ant", "orderSchedules.ordersInAdvanceTooltip": "Velg hvor mange fremtidige ordre systemet kontinuerlig skal sikre eksisterer. Systemet vil sjekke daglig om det trengs nye ordre for å opprettholde riktig antall fremtidige ordre.", "orderSchedules.dateLabel": "<PERSON>g i måned", "orderSchedules.nthLabel": "På den", "orderSchedules.nthWeekdayLabel": "Ukedag", "orderSchedules.nthWeekItem.1": "<PERSON><PERSON><PERSON><PERSON>", "orderSchedules.nthWeekItem.2": "<PERSON>", "orderSchedules.nthWeekItem.3": "T<PERSON><PERSON>", "orderSchedules.nthWeekItem.4": "<PERSON><PERSON><PERSON>", "orderSchedules.nthWeekItem.5": "<PERSON><PERSON>", "orderSchedules.useDateLabel": "Bruk dato", "orderSchedules.nthWeekItem.suffix": "en", "orderSchedules.lastDay": "Siste dag i måneden", "orderSchedules.enableCustomerConfirmation": "Skru av spørreskjema for kunden", "orderSchedules.enableCustomerNotifications": "Skru av SMS-varslinger for kunden", "orderSchedules.noWeekdaysSelected": "Vennligst velg minst én ukedag", "orderSchedules.missingEvery": "Du må velge en frekvens for å se forslag til repetisjon", "orderSchedules.createNewScheduleAtAddOrder": "Opprett repeterende ordre", "orderSchedules.main_product_name": "Tjeneste", "orderSchedules.orderScheduleId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orderSchedules.customerName": "Kunde", "orderSchedules.source": "Opprettet fra", "orderSchedules.status": "Status", "orderSchedules.fixedPayment": "Fast betaling", "orderSchedules.nextExecution": "Neste utførelse", "orderSchedules.createdAt": "Opprettet", "orderSchedules.deleteModal.title": "Kansellere eksisterende ordre?", "orderSchedules.deleteModal.body": "Ønsker du å kansellere fremtidige ikke-påbegynte ordre som allerede er opprettet av denne serien?", "orderSchedules.frequencyExplainer": "F.eks. en jobb som starter onsdag 20. september ville gjenta", "orderSchedules.frequencyExplainer2": " på Onsdag", "orderSchedules.nameExplainer": "Gi dette gjentakende alternativet et navn som vil være synlig for kunden", "orderSchedules.deleteModal.switchLabel": "Kanseller allerede opprettede ordre", "orderSchedules.first.workOrder": "<PERSON><PERSON><PERSON><PERSON>", "orderSchedules.first.payment": "<PERSON><PERSON><PERSON><PERSON>", "orderSchedules.first.consolidatedInvoice": "F<PERSON><PERSON>e samlefaktura", "orderSchedules.last.workOrder": "<PERSON><PERSON>b", "orderSchedules.last.payment": "<PERSON><PERSON> betaling", "orderSchedules.total": "Totalt", "orderSchedules.startTime": "Oppstart", "orderSchedules.estimatedDuration": "<PERSON><PERSON><PERSON><PERSON> varighet", "orderSchedules.startDate": "Startdato", "orderSchedules.nextJobAt": "Neste jobb", "orderSchedules.changeSchedule": "<PERSON>re serie", "orderSchedules.detailsCard.title": "Repeterende ordre", "orderSchedules.acceptSchedule": "Aksepter serie", "orderSchedules.scheduleAccepted": "Serie akseptert", "orderSchedules.acceptScheduleCustomer": "Aksepter serie for kunde", "orderSchedules.scheduleAcceptedCustomer": "Serie akseptert av kunde", "orderSchedules.activate": "Aktiver ordreserie", "orderSchedules.deactivate": "Pause ordreserie", "orderSchedules.cancelSchedule": "Kanseller serie", "orderSchedules.forceOrderCreation": "<PERSON><PERSON><PERSON><PERSON> ordre manuelt", "orderSchedules.orders.title": "Ordre", "orderSchedules.orders.noData": "Ordre opprettes når serien er aktivert", "orderSchedules.orders.upcomingOrders": "Kommende ordre", "orderSchedules.orders.noUpcomingOrders": "Ingen kommende ordre", "orderSchedules.orders.pastOrders": "Tidligere ordre", "orderSchedules.orders.noPastOrders": "Ingen tidligere ordre", "orderSchedules.orders.fixedPayments": "<PERSON><PERSON> betalinger", "orderSchedules.orders.noFixedPayments": "Ingen faste betalinger", "orderSchedules.orders.fixedPayments.instanceId": "Betalings-ID", "orderSchedules.orders.fixedPayments.createdAt": "Opprettet", "orderSchedules.orders.fixedPayments.invoiceSentAt": "Fak<PERSON> sendt", "orderSchedules.orders.fixedPayments.capturedAt": "Faktura betalt", "orderSchedules.paymentMethodChosen": "er forh<PERSON><PERSON><PERSON><PERSON> for denne ordreserien", "orderSchedules.payment": "Betalingsmetode", "orderSchedules.payment.fixedPayment": "Denne serien er satt opp med fast betaling.", "orderSchedules.payment.fixedPayment.invoiceSent": "Faktura sendes den", "orderSchedules.payment.fixedPayment.inactive": "Fast betaling er deaktivert. Aktiver ordreserie for å aktivere den faste betalingen.", "orderSchedules.payment.fixedPayment.monthEvery": "hver", "orderSchedules.payment.fixedPayment.instances": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> betalinger", "orderSchedules.payment.fixedPayment.instances.sent": "Fak<PERSON> sendt", "orderSchedules.payment.fixedPayment.noInstances": "Serien har ingen ubetalte aktive betalinger", "orderSchedules.startAfter": "Start etter", "orderSchedules.startAfterTooltip": "Det vil ikke opprettes noe før denne datoen", "orderSchedules.stopAfter": "<PERSON><PERSON> etter", "orderSchedules.stopAfterTooltip": "Det vil ikke opprettes noe etter denne datoen", "orders.overview.archiveSelected": "<PERSON><PERSON> valgte ordre", "orders.overview.cancelSelected": "<PERSON><PERSON><PERSON> valgte ordre", "orders.actions": "<PERSON><PERSON>", "orders.addNewOrder": "Legg til ordre", "orders.all": "Alle", "orders.unpaid": "Ubetalt", "orders.clear": "<PERSON><PERSON><PERSON>", "orders.archivedBtn": "Arkivert", "orders.accountingUnsyncedBtn": "Ikke ført i regnskap", "orders.today": "I dag", "orders.last7Days": "Siste 7 dager", "orders.last30Days": "Siste 30 dager", "orders.last90Days": "Siste 90 dager", "orders.last120Days": "<PERSON>ste 120 dager", "orders.customDays": "Custom", "notifications.markAllAsRead": "<PERSON><PERSON> alle som lest", "orders.filterEmployee": "<PERSON><PERSON><PERSON>", "orders.filterProducts": "<PERSON>du<PERSON><PERSON>", "orders.filterDate": "Utførelsesdato", "orders.jobDate": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderNumber": "Ordrenummer", "orders.orderDate": "Utførelsesdato", "orders.createdDate": "Oppret<PERSON><PERSON> dato", "orders.asc": "Stigende", "orders.desc": "Synkende", "orders.sortBy": "Sorter etter", "orders.custom": "Egendefinert", "orders.starting": "Start", "orders.ending": "Slutt", "orders.apply": "Bruk", "orders.reset": "Tilbakestill", "settings.company.customerCancelWorkOrder": "Tillat kunder å avbestille jobber", "orders.accountingUnsynced": "Ikke ført i regnskap", "orders.confirmation": "Bekreftelse", "orders.createNewOrder.addresses": "<PERSON><PERSON><PERSON>", "orders.createNewOrder.businessCustomer.search": "<PERSON><PERSON><PERSON> på adresse", "orders.createNewOrder.createOrder": " Lag ordre", "orders.createNewOrder.customer": "Kunde", "orders.createNewOrder.employee": "Ansatt og Ressurser", "orders.createNewOrder.schedule": "Tidsplan", "orders.createNewOrder.service": "Tjeneste", "orders.createNewOrder.title": "<PERSON><PERSON><PERSON><PERSON> ordre", "orders.itemName": "Ordre", "orders.quoteSent": "Til<PERSON>d sendt", "orders.repeating": "Visning for repeterende", "orders.quoteNotSent": "Tilbud ikke sendt", "orders.quoteAll": "Alle", "orders.newOrder.address.noAddresses": "Denne ordren har ingen adresser enda", "orders.newOrder.address.addressNotSaved": "Adressen er ikke lagret. Du må enten velge en adresse fra menyen i søkefeltet eller opprette en ny adresse", "orders.newOrder.address.noLatLng": "<PERSON>ne adressen har ikke lengde -eller breddegrad, og kan derfor ikke vises i kartet", "orders.newOrder.address.noStreetView": "Street view er ikke tilgjenge<PERSON>g for denne adressen", "orders.newOrder.address.addressName": "Addressenavn", "orders.newOrder.address.bathrooms": "Bad", "orders.newOrder.address.bedrooms": "Soverom", "orders.newOrder.address.elevator": "<PERSON><PERSON>", "orders.newOrder.address.floors": "<PERSON><PERSON><PERSON>", "orders.newOrder.address.floor": "<PERSON><PERSON><PERSON>", "orders.newOrder.address.garage": "Garasje", "settings.billing.dueDate": "Forfallsdato", "notifications.categories.generic": "Generisk", "notifications.categories.order": "Ordre", "notifications.categories.payment": "<PERSON><PERSON>", "notifications.categories.workOrder": "Arbeidsordre", "notifications.categories.note": "Notat", "notifications.categories.rating": "Anmeldelse", "orders.newOrder.address.search": "Søk...", "orders.newOrder.address.sectionID": "Leilighetsnummer", "orders.newOrder.address.selectSection": "<PERSON><PERSON><PERSON>", "orders.newOrder.address.size": "<PERSON><PERSON><PERSON><PERSON>", "orders.newOrder.address.totalRooms": "Totalt antall rom", "orders.newOrder.address.sectionId": "Leilighetsnr", "orders.newOrder.address.type": "Type", "orders.newOrder.address.yes": "<PERSON>a", "orders.newOrder.addressSection.addAddressStage": "Legg til ny adresse", "orders.newOrder.addressSection.addOrder": "Legg til ordre", "orders.newOrder.addressSection.address": "<PERSON><PERSON><PERSON>", "orders.newOrder.addressSection.setAddressLater": "Sett adresse senere", "orders.newOrder.addressSection.addressPrefix": "<PERSON><PERSON><PERSON> for", "orders.newOrder.addressSection.back": "Tilbake", "orders.newOrder.addressSection.close": "Lukk", "orders.newOrder.addressSection.creatingOrder": "Lager ordre...", "orders.newOrder.addressSection.durationHours": "<PERSON><PERSON><PERSON><PERSON>", "orders.newOrder.addressSection.executionDate": "Utførelsesdato", "orders.newOrder.addressSection.orderCreated": "Ordre opprettet!", "orders.newOrder.addressSection.saveOrder": "Lagre ordre", "orders.newOrder.addressSection.startDate": "Starttid", "orders.newOrder.customer.customerInformation": "Legg til kunde", "orders.newOrder.customer.enterCustomerName": "Navn eller telefonnummer", "orders.newOrder.customer.fillFormBelow": "<PERSON><PERSON><PERSON> etter e<PERSON>, eller opprett ny kunde", "orders.newOrder.customer.newCustomer": "Ny kunde", "orders.newOrder.customer.next": "Neste", "orders.newOrder.customer.noMatchesFound": "Ingen kunder funnet", "orders.newOrder.customer.searchForCustomer": "<PERSON><PERSON><PERSON> etter kunde", "orders.newOrder.customer.searching": "<PERSON><PERSON><PERSON>...", "orders.newOrder.newCustomer.addNewCustomer": "Legg til kunde", "orders.newOrder.newCustomer.anErrorOccurred": "Det har oppstått en feil. Kontakt administrator.", "orders.newOrder.newCustomer.back": "Tilbake", "orders.newOrder.newCustomer.customerInformation": "Kundeinformasjon", "orders.newOrder.newCustomer.email": "E-post", "orders.newOrder.newCustomer.fillFormBelow": "F<PERSON>l ut skjemaet for å opprette kunde", "orders.newOrder.newCustomer.name": "Navn", "orders.newOrder.newCustomer.firstName": "Fornavn", "orders.newOrder.newCustomer.lastName": "Etternavn", "orders.newOrder.newCustomer.phone": "Telefon", "orders.newOrder.newCustomer.saveCustomer": "Lagre kunde", "orders.newOrder.newCustomer.userExists": "Brukeren eksisterer allerede, sk<PERSON><PERSON><PERSON> har blitt oppdatert.", "orders.newOrder.priceRules.activatePriceRule": "Aktiver en prisregel", "orders.newOrder.priceRules.activePriceRules": "Aktive prisregler", "orders.newOrder.priceRules.noPriceRulesSelected": "Ingen prisregler valgt", "orders.newOrder.priceRules.priceRules": "<PERSON><PERSON><PERSON><PERSON>", "orders.newOrder.products.back": "Tilbake", "orders.newOrder.products.next": "Neste", "orders.newOrder.products.title": "Velg produkt", "orders.orderDetails.acceptOrderButton": "<PERSON><PERSON> ordre", "orders.orderDetails.customer.refreshPrices.title": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.partner.refreshPrices.title": "Partnerpriser", "orders.orderDetails.customer.refreshPrices.bold": "Denne ordren inneholder produkter hvor denne kunden har spesialpriser.", "orders.orderDetails.partner.refreshPrices.bold": "<PERSON>ne ordren inneholder produkter hvor denne partneren har spesialpriser.", "orders.orderDetails.refreshPrices.regular": "Vil du oppdatere ordrelinjene i denne ordren med disse prisene?", "orders.orderDetails.initiateOrderButton": "Initiér ordre", "orders.orderDetails.printQuote": "Print tilbud", "orders.orderDetails.printWorkOrder": "Print arbeidsordre", "orders.orderDetails.acceptanceInfo": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.acceptedBy": "Akseptert av", "orders.orderDetails.acceptedCustomer": "kunde", "orders.orderDetails.acceptedEmployee": "ansatt", "orders.orderDetails.accountingError": "F<PERSON>ring i regnskap feilet", "orders.orderDetails.accountingPaymentNotMade": "<PERSON>ling ikke fullført enda", "orders.orderDetails.accountingStatus": "Regnskapsstatus", "orders.orderDetails.accountingStatus.order": "Ordre", "orders.orderDetails.accountingStatus.getPdf": "Last ned bilag", "orders.orderDetails.accountingStatus.noAccounting": "Ingen poster er blitt lagt ut i regnskap.", "orders.orderDetails.accountingStatus.payment": "<PERSON><PERSON>", "orders.orderDetails.accountingStatus.checkPayment": "He<PERSON> siste beta<PERSON><PERSON>tus fra regnskap", "orders.orderDetails.accountingSuccess": "F<PERSON>rt i regnskap", "orders.orderDetails.addressCard.bathrooms": "Bad", "orders.orderDetails.addressCard.shedSize": "<PERSON><PERSON>", "orders.orderDetails.addressCard.bedrooms": "Soverom", "orders.orderDetails.addressCard.addressName": "Beskrivelse", "orders.orderDetails.addressCard.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.addressCard.change": "<PERSON><PERSON>", "orders.orderDetails.addressCard.closeButton": "Lukk", "orders.orderDetails.addressCard.durationHours": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.addressCard.editAddress": "<PERSON>iger adresse", "orders.orderDetails.addressCard.editAddressNote": "NB! Hvis du endrer noen av disse verdiene vil ordren ikke lenger være tildelt et mannskap", "orders.orderDetails.addressCard.editAddressTitle": "<PERSON>iger adresse", "orders.orderDetails.addressCard.editButton": "<PERSON><PERSON>", "orders.orderDetails.addressCard.elevator": "<PERSON><PERSON>", "orders.orderDetails.addressCard.executionDate": "Utførelsesdato", "orders.orderDetails.addressCard.floors": "<PERSON><PERSON><PERSON>", "orders.orderDetails.addressCard.floor": "<PERSON><PERSON><PERSON>", "orders.orderDetails.addressCard.garage": "Garasje", "orders.orderDetails.addressCard.newAddress": "<PERSON><PERSON> adresse", "orders.orderDetails.addressCard.saveButton": "Lagre", "orders.orderDetails.addressCard.size": "Areal", "orders.orderDetails.addressCard.totalRooms": "Totalt antall rom", "orders.orderDetails.addressCard.type": "Type", "orders.orderDetails.addressCard.totalDistance": "Total distanse", "orders.orderDetails.addressCard.totalTime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.addressFrom": "<PERSON><PERSON><PERSON> fra", "orders.orderDetails.addressInformation": "Adresseinformasjon", "orders.orderDetails.addressRouteTab": "Adresse og kjørerute", "orders.orderDetails.addressRoutes.movingRoute": "Rute", "orders.orderDetails.addressRoutes.movingRouteMessage": "Flytteruten er ikke tilgjengelig for de gitte adressene", "orders.orderDetails.addressRoutes.streeViewMessage": "Street view er ikke tilgjenge<PERSON>g for denne adressen", "orders.orderDetails.addressRoutes.title": "Gatevisning", "orders.orderDetails.addressTo": "<PERSON><PERSON><PERSON> til", "orders.orderDetails.sendQuote.header": "Send tilbud", "orders.orderDetails.sendQuote.selectSendMethod": "Velg hvordan tilbudet skal sendes", "orders.orderDetails.sendQuote.noPhone": "Det er ikke registrert noe telefonnummer på mottakeren.", "orders.orderDetails.sendQuote.foreignPhone": "Det er registrert et utenlandsk telefonnummer på mottakeren. Du kan ikke sende tilbud på SMS til utenlandske nummer", "orders.orderDetails.sendQuote.noAffiliateContact": "Du må legge til en bedriftskontakt på denne ordren for å kunne sende tilbud på SMS.", "orders.orderDetails.sendQuote.noEmail": "Det er ikke registrert noen e-post på mottakeren.", "orders.orderDetails.sendQuote.sms": "Send på SMS", "orders.orderDetails.sendQuote.email": "Send på e-post", "orders.orderDetails.sendQuote.noPayment": "Det er ikke registrert noen betalingsmettode på denne ordren. Er du sikker på at du vil sende tilbudet?", "orders.orderDetails.SendCustomerConfirmation.header": "Ordrebekreftelse", "orders.orderDetails.SendCustomerConfirmation.toggle": "Send ordrebekreftelse på epost til kunde", "order.orderDetails.quoteHiddenPriceModal.title": "<PERSON><PERSON><PERSON> er skjult", "order.orderDetails.quoteHiddenPriceModal.boldText": "<PERSON>gt partner ha<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, og er satt som betalingsmottaker.", "order.orderDetails.quoteHiddenPriceModal.regularText": "Kunden vil derfor ikke kunne se noen priser for denne ordren, og heller ikke akseptere tilbudet i kundeportalen. Ordren må derfor aksepteres manuelt i Core.", "order.orderDetails.quoteHiddenPriceModal.yes": "Send tilbud", "order.orderDetails.quoteHiddenPriceModal.no": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order.orderDetails.finishJobVerifyModal.boldText": "<PERSON><PERSON> <PERSON> fullføre jobben kan timeantallet endres.", "order.orderDetails.finishJobVerifyModal.regularText": "<PERSON><PERSON><PERSON> du fullfører jobben settes antall timer til det faktiske antall timer som er registrert. Husk å dobbeltsjekke ordrelinjen før du sender til betaling.", "order.orderDetails.finishJobVerifyModal.yes": "Fortsett", "order.orderDetails.finishJobVerifyModal.no": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.addPaymentRecipient.header": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "orders.orderDetails.paymentRecipient.modal.title": "<PERSON><PERSON><PERSON>", "orders.orderDetails.paymentRecipient.modal.customer": "Velg kunde", "orders.orderDetails.paymentRecipient.modal.paymentRecipient": "<PERSON><PERSON><PERSON>", "orders.orderDetails.paymentRecipient.modal.serviceRecipient": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.paymentRecipient.modal.orSet": "Sett betalingsmottaker og tjenestemottaker individuelt", "orders.orderDetails.addPaymentRecipient.description": "Mottakeren vil få tilsendt en SMS med en link til betalingssiden.", "order.orderDetails.sendToPaymentVerifyModal.boldText": "Ordren vil låses når den sendes til betaling.", "order.orderDetails.sendToPaymentVerifyModal.regularText": "<PERSON>tter ordren sendes til betaling vil det ikke lenger være mulig å redigere den. Denne handlingen kan ikke angres.", "order.orderDetails.sendToPaymentVerifyModal.regularSecondaryText": "NB: Det er ikke registrert noe telefonnummer på mottakeren, og kunden vil derfor ikke motta betalings-SMS.", "order.orderDetails.sendToPaymentVerifyModal.yes": "Send", "order.orderDetails.sendToPaymentVerifyModal.no": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order.orderDetails.sendToPaymentVerifyModal.fixedPriceOrderLines.title": "Følgende ordrelinjer har kvantitetsstyrt fastpris", "order.orderDetails.sendToPaymentVerifyModal.fixedPriceOrderLines.description": "Ønsker du å konvertere enhetsantall til 1 for disse?", "order.orderDetails.sendToPaymentVerifyModal.fixedPriceOrderLines.subtext": "Dette vil ikke påvirke ordrelinjenes totalpris", "order.orderDetails.sendToPaymentVerifyModal.fixedPriceOrderLines.convert": "Konverter", "order.orderDetails.sendToPaymentVerifyModal.fixedPriceOrderLines.converted": "Ordrelinjer er konvertert", "orders.orderDetails.archiveOrder": "<PERSON><PERSON> ordre", "orders.orderDetails.cancelOrder": "<PERSON><PERSON>ller ordre", "orders.orderDetails.deleteOrder": "Slett ordre", "orders.orderDetails.duplicateOrder": "<PERSON><PERSON><PERSON> ordre", "orders.orderDetails.cargo.itemsTotal": "artik<PERSON> totalt", "orders.orderDetails.cargo.title": "Gods", "orders.orderDetails.cargoTab": "Gods", "orders.orderDetails.checklistsTab": "Rapporter og sjekklister\n", "orders.orderDetails.confirmationStatus": "Status på spørreskjema", "orders.orderDetails.subcontractors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.uploadedBy": "Opplastet av", "orders.orderDetails.attachments.invoiceAttached": "Vedlegget er lagt til ved faktura", "orders.orderDetails.attachments.cannotDelete": "Vedleg<PERSON> er lagt til ved faktura, og kan ikke slettes", "orders.orderDetails.attachments.attachToInvoice": "Legg ved faktura", "orders.orderDetails.attachments.modal.title": "Last opp fil", "orders.orderDetails.attachments.modal.uploading": "Laster opp...", "orders.orderDetails.attachments.modal.button": "Velg fil", "orders.orderDetails.attachments.modal.dragHere": "<PERSON>a og slipp filen her", "orders.orderDetails.attachments.modal.dropHere": "<PERSON><PERSON><PERSON> filen for å laste opp", "orders.orderDetails.attachments.modal.or": "eller", "orders.orderDetails.customerHasNotRated": "Ingen tilbakemelding gitt av kunden", "orders.orderDetails.customerHasNotRatedYet": "<PERSON>nden har ikke svart enda", "orders.orderDetails.customerRating": "Anmeldelse", "orders.orderDetails.addServiceRecipient": "<PERSON>gg til som tjenestemottaker", "order.orderDetails.orderScheduleLabel": "<PERSON>ne ordren er en del av en ordreserie", "order.orderDetails.orderScheduleLink": "Gå til ordreserie", "orders.orderDetails.detailsCard.closeButtonLabel": "Lukk", "orders-orderDetails-detailsCard-repeatingJobs": "<PERSON><PERSON><PERSON><PERSON> jobber", "orders.orderDetails.detailsCard.date": "<PERSON><PERSON>ld<PERSON> dato", "orders.orderDetails.detailsCard.durationHoursLabel": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.detailsCard.durationLabel": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.detailsCard.nextExecution": "Neste utførelse", "orders.orderDetails.detailsCard.nextExecution.notPlanned": "Denne repeterende jobben er ikke planlagt enda", "orders.orderDetails.detailsCard.editButtonLabel": "<PERSON><PERSON>", "orders.orderDetails.detailsCard.editOrderNote": "NB! Hvis du endrer noen av disse verdiene vil ordren ikke lenger være tildelt et mannskap", "orders.orderDetails.detailsCard.editOrderTitle": "Rediger ordre", "orders.orderDetails.detailsCard.executionDateLabel": "Utførelsesdato", "orders.orderDetails.detailsCard.startDateLabel": "Jobber skal opprettes etter", "orders.orderDetails.detailsCard.executionDateMultiLabel": "<PERSON><PERSON><PERSON><PERSON> for", "orders.orderDetails.detailsCard.dateMultiLabel": "Da<PERSON>", "orders.orderDetails.detailsCard.startTimeMultiLabel": "Starter", "orders.orderDetails.detailsCard.endTimeMultiLabel": "Slutter", "orders.orderDetails.detailsCard.deleteEvent": "<PERSON><PERSON> hendelse", "orders.orderDetails.detailsCard.from": "Varighet fra", "orders.orderDetails.detailsCard.hours": "Timer", "orders.orderDetails.detailsCard.jobDateLabel": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.detailsCard.jobTimeLabel": "Oppstartstidspunkt", "orders.orderDetails.detailsCard.range": "Intervall", "orders.orderDetails.detailsCard.required": "Vennligst oppgi en gyldig varighet", "orders.orderDetails.detailsCard.saveButtonLabel": "Lagre", "orders.orderDetails.detailsCard.title": "Ordre", "orders.orderDetails.detailsCard.to": "Varighet til", "orders.orderDetails.createWorkOrder.title": "<PERSON><PERSON><PERSON><PERSON>b", "orders.orderDetails.createWorkOrder.schedule.title": "Op<PERSON>rett repeterende jobb", "orders.orderDetails.createWorkOrder.copyAddresses": "<PERSON><PERSON><PERSON> adresser fra ordre", "orders.orderDetails.createWorkOrder.workOrderTitleLabel": "Tittel for jobb (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "orders.orderDetails.createWorkOrder.workOrderDescriptionLabel": "Arbeidsbeskrivelse (Valgfritt)", "orders.orderDetails.editWorkOrder.title": "<PERSON><PERSON>b", "orders.orderDetails.editWorkOrder.schedule.title": "<PERSON><PERSON> repeterende jobb", "orders.orderDetails.duplicateAddress.deleteAddress": "<PERSON><PERSON> adresse", "orders.orderDetails.duplicateAddress.disabledToolTip": "<PERSON><PERSON><PERSON> kan ikke legges til etter at jobben er ferdig", "orders.orderDetails.duplicateAddress.selectAddress": "<PERSON><PERSON><PERSON> etter adresse", "orders.orderDetails.duplicateAddress.selectStage": "Velg trinnet du vil legge til addressen på", "orders.orderDetails.duplicateAddress.title": "Legg til adresse", "orders.orderDetails.editButton": "<PERSON><PERSON>", "orders.orderDetails.emailLabel": "E-post:", "orders.orderDetails.externalPayment": "<PERSON><PERSON><PERSON> betaling", "orders.orderDetails.hiddenPricesLabel": "<PERSON><PERSON><PERSON>", "orders.orderDetails.hiddenPricesTooltip": "<PERSON>nden vil ikke kunne se noen priser for denne ordren ettersom valgt partner har '<PERSON><PERSON><PERSON><PERSON> prise<PERSON>' <PERSON><PERSON><PERSON><PERSON>, og er satt som betalingsmottaker.", "orders.orderDetails.externalPaymentTooltip": "Sett betalingen av denne ordren til å håndteres eksternt utenfor den vanlige betalingsflyten", "orders.orderDetails.externalPaymentWarning": "På grunn av ordrens status vil ordren bli avsluttet og merket som betalt hvis den sendes til ekstern betaling. Er du sikker på at du vil fortsette?", "orders.orderDetails.finishJob": "Merk jobb som ferdig", "orders.orderDetails.finishJobTooltip": "Overstyr crew-appen og merk jobben som ferdig", "orders.orderDetails.notAccepted": "<PERSON><PERSON>ke aks<PERSON> enda", "orders.orderDetails.notesOrderInfoTab": "Notater og ordreinfo", "orders.orderDetails.orderAcceptedMessage": "Ordre er akseptert", "orders.orderDetails.orderNotes.NotCustomerQuestionnaire": "<PERSON><PERSON> spørsmå<PERSON> tilg<PERSON>g", "orders.orderDetails.orderNotes.fromCustomerPortal": "Spørsmål fra kundeportal", "orders.orderDetails.orderNotes.fromEmbed": "Spørsmål fra bestillingsskjema", "orders.orderDetails.orderNotes.addNote": "Legg til", "orders.orderDetails.orderNotes.customerInput": "<PERSON><PERSON><PERSON> svar", "orders.orderDetails.orderNotes.customerNotResponded": "<PERSON>nden har ikke svart", "orders.orderDetails.orderNotes.customerQuestionnaire": "Spørreskjema", "orders.orderDetails.orderNotes.embedCustomerQuestionnaire": "Spørreskjema fra booking", "orders.orderDetails.orderNotes.daysAgo": "dager siden", "orders.orderDetails.orderNotes.deleted": "<PERSON><PERSON><PERSON>", "orders.orderDetails.orderNotes.description": "Vennligst legg til notater.", "orders.orderDetails.orderNotes.external": "Synlig for kunde", "orders.orderDetails.orderNotes.hoursAgo": "timer siden", "orders.orderDetails.orderNotes.internal": "Intern", "orders.orderDetails.orderNotes.internalNote": "Internt notat", "orders.orderDetails.orderNotes.internalNoteToolTip": "Interne notater vil ikke være synlige for kunden, kun for selskapets ansatte", "orders.orderDetails.orderNotes.justNow": "nå nylig", "orders.orderDetails.orderNotes.messageDeletedBy": "Melding slettet av", "orders.orderDetails.orderNotes.minutesAgo": "minutter siden", "orders.orderDetails.orderNotes.secondsAgo": "sekunder siden", "orders.orderDetails.orderNotes.title": "Notater", "orders.orderDetails.orderNotes.updated": "Oppdatert", "orders.orderDetails.orderNotes.visibleToCustomerToolTip": "Notater merket som 'Synlig for kunde' vil bli vist for kunden. Umerkede notater er kun synlige for selskapets ansatte", "orders.orderDetails.orderNumber": "Ordre #", "orders.orderDetails.orderPartner.closeButtonLabel": "Lukk", "orders.orderDetails.orderPartner.delete": "<PERSON><PERSON> partner", "orders.orderDetails.orderPartner.disabledToolTip": "Partner kan ikke endres etter at ordren er sendt til betaling", "orders.orderDetails.orderPartner.partner": "Partner", "orders.orderDetails.orderPartner.paymentRecipient": "Betalingsmottaker", "orders.orderDetails.orderPartner.saveButtonLabel": "Lagre", "orders.orderDetails.orderPartner.searchForPartner": "<PERSON><PERSON><PERSON> etter partner", "orders.orderDetails.orderPartner.searchLabel": "Skriv inn navnet på partneren", "orders.orderDetails.orderPartner.title": "<PERSON>gg til partner", "orders.orderDetails.orderPartner.titleSubtext": "<PERSON><PERSON><PERSON> etter partner ne<PERSON><PERSON><PERSON> for å legge til i ordren", "orders.orderDetails.orderProducts.addCustomProduct": "Egendefinert produkt", "orders.orderDetails.orderProducts.editProduct": "Rediger produkt", "orders.orderDetails.orderProducts.addPricerule": "<PERSON><PERSON> til p<PERSON>regel", "orders.orderDetails.orderProducts.chooseProductVariant": "Velg produktvariant", "orders.orderDetails.orderProducts.noProductVariantSelected": "Ingen produktvariant lagt til", "orders.orderDetails.orderProducts.productVariants": "Produktvarianter", "orders.orderDetails.orderProducts.activeAProductVariants": "Aktiver produktvarianter", "orders.orderDetails.orderProducts.activProductVariants": "Aktive produktvarianter", "orders.orderDetails.orderProducts.addProduct": "<PERSON>gg til produkt", "orders.orderDetails.orderProducts.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.orderProducts.cancelCustomProduct": "Tilbake", "orders.orderDetails.orderProducts.customProductPrice": "<PERSON><PERSON>", "orders.orderDetails.orderProducts.customProductTitle": "Navn", "orders.orderDetails.orderProducts.customProductModalTitle": "Egendefinert produkt", "orders.orderDetails.orderProducts.discount": "<PERSON><PERSON><PERSON>", "orders.orderDetails.orderProducts.refundOrderLine": "Refusjon", "orders.orderDetails.orderProducts.discountAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.orderProducts.edit": "endre", "orders.orderDetails.orderProducts.grandTotal": "Delsum", "orders.orderDetails.orderProducts.id": "ID", "orders.orderDetails.orderProducts.netPayment": "Netto betaling", "orders.orderDetails.orderProducts.noManualPriceRule": "Ingen manuelle priser til<PERSON> for denne ordrelinjen.", "orders.orderDetails.orderProducts.noPriceRules": "Ingen manuelle prisregler tilgjengelig for dette produktet", "orders.orderDetails.orderProducts.orderSummary": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.orderProducts.outOfStock": "<PERSON>k<PERSON> tilg<PERSON>g på lager", "orders.orderDetails.orderProducts.performRefund": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.orderProducts.price": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.orderProducts.product": "Produkt", "orders.orderDetails.orderProducts.quantity": "<PERSON><PERSON><PERSON>", "orders.orderDetails.orderProducts.quantityRequired": "<PERSON><PERSON><PERSON> m<PERSON> oppgis", "orders.orderDetails.orderProducts.useFixedPrice": "Bruk fastpris", "orders.orderDetails.orderProducts.fixedPrice": "Fast<PERSON>ris", "orders.orderDetails.orderProducts.setTotalPrice": "Fast<PERSON>ris", "orders.orderDetails.orderProducts.refund": "Refunder", "orders.orderDetails.orderProducts.refundEditQuantityNotAllowedFixedPrice": "<PERSON>ttersom en prisregel som justerer totalprisen er i bruk kan ikke kvantiteten for refusjonen settes manuelt", "orders.orderDetails.orderProducts.refundNotAllowedQuantity": "Ordrelinjens kvantitet er for lav til å kunne refundere", "orders.orderDetails.orderProducts.refundPaymentMethodNotAllowed": "Refusjoner er ikke mulig for denne betalingsmetoden", "orders.orderDetails.orderProducts.refunded": "Refundert", "orders.orderDetails.orderProducts.refundedAmount": "Refunder<PERSON> beløp", "orders.orderDetails.orderProducts.save": "Lagre", "orders.orderDetails.orderProducts.remove": "<PERSON><PERSON><PERSON>", "orders.orderDetails.orderProducts.selectProduct": "Velg produkt", "orders.orderDetails.orderProducts.setDiscount": "<PERSON><PERSON> rabatt", "orders.orderDetails.orderProducts.modalHeader": "<PERSON><PERSON>g ordrelinjen du vil rabattere", "orders.orderDetails.orderProducts.produtcs": "<PERSON>du<PERSON><PERSON>", "orders.orderDetails.orderProducts.unitPrice": "Produktpris", "orders.orderDetails.orderProducts.setDiscountDisabledTooltip": "<PERSON><PERSON><PERSON> kan ikke endres etter at ordren er sendt til betaling", "orders.orderDetails.orderProducts.discountAmountAlreadySetTooltip": "Ordre med rabatt-ordrelinjer kan ikke settes opp med prosentvis rabatt. Slett rabatt-ordrelinjene først.", "orders.orderDetails.orderProducts.sorry": "Beklager", "orders.orderDetails.orderProducts.submitCustomProduct": "Legg til", "orders.orderDetails.orderProducts.total": "Totalt", "orders.orderDetails.orderProducts.usePercent": "B<PERSON> prosent", "orders.orderDetails.orderProducts.salesPriceExVat": "Total eks. MVA", "orders.orderDetails.orderProducts.vatAmount": "MVA", "orders.orderDetails.orderProducts.totalPricePopup.bold": "<PERSON><PERSON> <PERSON> sette fastpris vil all automatisering for ordrelinjen fjernes.", "orders.orderDetails.orderProducts.totalPricePopup.regular": "<PERSON><PERSON> inn<PERSON> at alle prisregler og eventuelle pris- eller kvantitetskalkuleringer vil bli fjernet. Dette kan ikke reverseres.", "orders.orderDetails.orderProducts.totalPricePopup.yes": "Fortsett", "orders.orderDetails.orderProducts.totalPricePopup.no": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.orderProducts.invalidAmount": "Ugyldig rabattbeløp", "orders.orderDetails.orderProducts.invalidAmountTooltip": "Rabattbeløpet er for lavt. Det laveste beløpet du kan sette på denne ordrelinjen er ", "orders.orderDetails.orderReports.extern": "Synlig for kunde", "orders.orderDetails.otherActions": "Flere valg", "orders.orderDetails.paymentInformation": "Betalingsinformasjon", "orders.orderDetails.paymentInformation.modal.title": "Forhåndsvelg betalingsmetode", "orders.orderDetails.paymentInformation.paymentMethod": "Betalingsmetode", "orders.orderDetails.paymentInformation.invoiceSettings": "Fakturainnstillinger", "orders.orderDetails.paymentInformation.default.modal.regular": "Kunden kan nå velge fritt mellom følgende alternativer når ordren sendes til betaling:", "orders.orderDetails.paymentInformation.default.modal.regularNoMethods": "VIKTIG: Ingen av betalingsmetodene i selskapet ditt er tilgjengelige for å velges av kunden. Hvis du ikke gjør en betalingsmetode tilgjengelig for kunden (eller endrer betalingsmetoden på denne ordren), vil kunden ikke kunne betale.", "orders.orderDetails.paymentInformation.external.modal.regularWithAccounting": "Betaling håndteres eksternt (eksempelvis med kortterminal eller gjennom kontantbetaling).", "orders.orderDetails.paymentInformation.external.modal.regularNoAccounting": "Betaling håndteres eksternt (eksempelvis med kortterminal, gjenn<PERSON> kont<PERSON>aling, eller ved faktura).", "orders.orderDetails.paymentInformation.invoice.modal.regular": "Faktura sendes ut via regnskapsprogram når ordren sendes til betaling.", "orders.orderDetails.paymentInformation.invoice.modal.regular.sent": "Faktura har blitt sendt til betalingsmottaker.", "orders.orderDetails.paymentStatusLabel": "Betalingsstatus:", "orders.orderDetails.fullyRefunded": "Refundert", "orders.orderDetails.partiallyRefunded": "<PERSON><PERSON> refundert", "orders.orderDetails.paymentStatus.markAsPaid": "Marker som betalt", "orders.orderDetails.fullyRefundedPendingInvoice": "<PERSON><PERSON><PERSON> sendt, ordre refundert", "orders.orderDetails.fullyRefundedAndPaid": "<PERSON><PERSON><PERSON> betalt, ordre refundert", "orders.orderDetails.partiallyRefundedPendingInvoice": "<PERSON><PERSON><PERSON> sendt, ordre delvis refundert", "orders.orderDetails.partiallyRefundedAndPaid": "<PERSON><PERSON><PERSON> betalt, ordre delvis refundert", "orders.orderDetails.paymentTypeLabelBeforeSentToPayment": "Forh<PERSON><PERSON><PERSON><PERSON> metode:", "orders.orderDetails.paymentTypeLabelAfterSentToPayment": "Betalingsmetode:", "orders.orderDetails.invoiceNotSent": "Faktura ikke sendt", "orders.orderDetails.invoiceSent": "Fak<PERSON> sendt", "orders.orderDetails.invoiceSendAgain": "Send på nytt", "orders.orderDetails.invoiceSettings.title": "Fakturainnstillinger", "orders.orderDetails.invoiceSettings.invoiceSendType": "Fakturatype", "orders.orderDetails.invoiceSettings.dueDate": "Forfallsdato", "orders.orderDetails.invoiceSettings.dueDatePostFix": "dager", "orders.orderDetails.invoiceSettings.invoiceReference": "Fakturareferanse", "orders.orderDetails.invoiceSettings.invoiceComment": "Fakturakommentar", "orders.orderDetails.invoiceSettings.invoiceEmail": "E-post for faktura", "orders.orderDetails.invoiceSettings.disabledLabel": "<PERSON><PERSON><PERSON> når faktura er sendt", "orders.orderDetails.phoneLabel": "Telefon:", "orders.orderDetails.refund.fullRefund": "Refunder hele ordren", "orders.orderDetails.refund.VAT": "MVA", "orders.orderDetails.refund.availableRefund": "tilgjengelig for refusjon", "orders.orderDetails.refund.cancelButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.refund.fixedPrice": "Fast<PERSON>ris", "orders.orderDetails.refund.id": "ID", "orders.orderDetails.refund.price": "<PERSON><PERSON>", "orders.orderDetails.refund.product": "Produkt", "orders.orderDetails.refund.oneRefundOnlyTitle": "Kun <PERSON>n refusjon tillatt", "orders.orderDetails.refund.oneRefundOnlyBody": "På grunn av interne begrensninger i Fiken er det kun mulig å gjøre én refusjon per betaling, uavhengig om den er delvis eller hel.", "orders.orderDetails.refund.productsSubTotal": "Delsum for produkter", "orders.orderDetails.refund.quantity": "<PERSON><PERSON><PERSON>", "orders.orderDetails.refund.reasonForRefund": "Årsak til refusjon", "orders.orderDetails.refund.refund": "Refunder", "orders.orderDetails.refund.refundAmount": "<PERSON><PERSON><PERSON> for refusjon", "orders.orderDetails.refund.refundSummary": "Oppsummering av refusjon", "orders.orderDetails.refund.refundTotal": "Totalt for refusjon", "orders.orderDetails.refund.sendRefundNotification": "Send en varsel til kunden", "orders.orderDetails.refund.total": "Totalt", "orders.orderDetails.refundButtonLabel": "Refusjon", "orders.orderDetails.sendConfirmationButtonLabel": "Send ordrebekreftelse på e-post", "orders.orderDetails.removeOrderFromArchive": "Hent fra arkiv", "orders.orderDetails.reports.extern": "Visible for costumer", "orders.orderDetails.reports.internal": "Intern", "orders.orderDetails.resources.crew": "Mannskap", "orders.orderDetails.resources.enlargedImage": "Forstørret bilde", "orders.orderDetails.resources.finished": "<PERSON><PERSON><PERSON>", "orders.orderDetails.resources.noCrewAssigned": "Ingen mannskap er tildelt", "orders.orderDetails.resources.noReportsMade": "Ingen elementer er blitt lagt til", "orders.orderDetails.resources.reports": "Rapport", "orders.orderDetails.resources.timeTracking": "Tidsregistrering", "orders.orderDetails.resources.timeTracking.toggleLabel": "Per ansatt", "orders.orderDetails.resources.timeTracking.noTrackings": "<PERSON>ben er ikke startet, ingen tidsregistrering tilgjengelig", "orders.orderDetails.resources.timeTracking.noCheckins": "Ingen ansatte har sjekket inn på denne jobben", "orders.orderDetails.resources.totalTime": "Total fakturerbar tid", "orders.orderDetails.resources.totalUserTime": "Totalt", "orders.orderDetails.resources.ongoing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.resourcesReportsTab": "Ansatte og ressurser", "orders.orderDetails.savingAddress": "Lagrer addresse...", "orders.orderDetails.sendQuoteButton": "Send tilbud", "orders.orderDetails.confirmManually": "Bekreft manuelt", "orders.orderDetails.confirmManually.withoutConfirmation": "Bekreft", "orders.orderDetails.confirmManually.withConfirmation": "Bekreft og send ordrebekreftelse", "orders.orderDetails.sendQuoteNoAffiliateContact": "Du må legge til eller opprette en bedriftskontakt for denne ordren for å sende et tilbud direkte fra Between", "orders.orderDetails.sendToPayment": "Send til betaling", "orders.orderDetails.creditInvoice": "Krediter faktura", "orders.orderDetails.creditInvoice.tooltip": "Fakturaer som er delvis refundert kan ikke krediteres", "orders.orderDetails.resetAccounting": "<PERSON><PERSON><PERSON>", "orders.orderDetails.captureManually": "<PERSON><PERSON> belø<PERSON> man<PERSON>t", "orders.orderDetails.markOrderAsPaid": "Merk som betalt", "orders.orderDetails.sendToSubContractor": "Send til underleverandør", "orders.orderDetails.sendToSubContractor.title": "Send jobb til underleverandør", "orders.orderDetails.sendToSubContractor.searchPlaceholder": "<PERSON><PERSON><PERSON> etter underleveran<PERSON>ør", "orders.orderDetails.sendPaymentSms": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "orders.orderDetails.sendToPaymentPaymentBtn": "Send til betaling", "orders.orderDetails.sendToPaymentPaymentTooltip": "Send ordren til betaling, dette kan kun gjøres når ordren er fullført og totalsum er større enn 0,-", "orders.orderDetails.sendToPaymentTooltip": "Send ordren til betaling, dette kan kun gjøres når ordren er fullført", "orders.orderDetails.sentToPaymentPaymentBtn": "<PERSON>t til betaling", "orders.orderDetails.goToReportinatorReport": "Gå til takstrapport", "orders.orderDetails.createToReportinatorReport": "<PERSON><PERSON><PERSON>t ny takstrapport for ordre", "orders.orderDetails.enableProject": "Aktiver prosjektvisning", "orders.orderDetails.disableProject": "<PERSON><PERSON><PERSON><PERSON>s<PERSON>", "orders.orderDetails.closeOrder": "Avslutt ordre", "order.orderDetails.closeOrderVerifyModal.boldText": "<PERSON>te vil avslutte ordren.", "order.orderDetails.closeOrderVerifyModal.regularText": "<PERSON><PERSON> du har utestående betalinger eller pågående jobber anbefaler vi å håndtere disse først.", "orders.orderDetails.stage.orderTasks.noTasks": "Ingen oppgaver i dette steget", "orders.orderDetails.stage.orderTasks.tasks": "Oppgaver", "orders.orderDetails.stage.stageNotStarted": "<PERSON>k<PERSON>", "orders.orderDetails.stage.stageOngoing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.stage.title": "<PERSON><PERSON>g steg", "orders.orderDetails.title": "Ordredetaljer", "orders.orderDetails.orderDiscount.title": "<PERSON><PERSON> r<PERSON>", "orderDetails.dateTime.reschedule": "<PERSON><PERSON> t<PERSON>punk<PERSON>", "orderDetails.dateTime.createSchedule": "Opprett serie", "orderDetails.dateTime.editSchedule": "<PERSON><PERSON>", "orderDetails.dateTime.dateTime": "Dato & tid", "orderDetails.dateTime.repeats": "Repeteres", "orderDetails.dateTime.addAnotherDate": "<PERSON>gg til tidspunkt", "orderDetails.comment.title": "Fakturakommentar", "orderDetails.comment.add": "Legg til fakturakommentar", "orderDetails.refundPayments.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orderDetails.payments.title": "<PERSON><PERSON>", "orderDetails.payments.add": "<PERSON><PERSON><PERSON><PERSON> betaling", "orderDetails.payments.noPayments": "Ingen betalinger er opprettet enda", "orderDetails.payments.invoiceDate": "Fakturadato", "orderDetails.payments.paymentStatusInvoiceNotSent": "Faktura ikke sendt", "orderDetails.payments.paymentMethod": "Betalingsmetode", "orderDetails.payments.dueDate": "Forfallsdato", "orderDetails.payments.invoiceEmail": "Faktura-epost", "orderDetails.payments.invoiceReference": "Fakturareferanse", "orderDetails.payments.invoiceSendType": "Sendingstype", "orderDetails.payments.paidAt": "<PERSON><PERSON> dato", "orderDetails.payments.paidAmount": "Innbetalt beløp", "orderDetails.payments.paymentStatus": "<PERSON>lings<PERSON><PERSON>", "orderDetails.payments.paymentRecipient": "<PERSON><PERSON><PERSON>", "orderDetails.payments.autoSendAt": "Sendes automatisk", "orderDetails.payments.notPaidYet": "<PERSON><PERSON><PERSON> betalt enda", "orderDetails.payments.setByConsolidatedInvoice": "Settes i samlefaktura", "orderDetails.payments.invoicePDF": "Faktura PDF", "orderDetails.payments.download": "Last ned", "orderDetails.payments.accountingStatus": "Regnskapsstatus", "orderDetails.payments.paymentAccountingStatus": "Regnskapsstatus betaling", "orderDetails.payments.paymentReference": "Betalingsreferanse", "orderDetails.payments.eventLog": "<PERSON><PERSON><PERSON> he<PERSON>g", "orderDetails.payments.project": "Prosjekt", "orderDetails.payments.department": "Avdeling", "orderDetails.payments.goToConsolidationContainer": "<PERSON><PERSON><PERSON> samlefaktura", "orderDetails.payments.projectAndDepartment": "Sett prosjekt og avdeling", "orderDetails.payments.projectAndDepartment.projectOnly": "Sett prosjekt", "orderDetails.payments.projectAndDepartment.title": "Sett prosjekt og avdeling for betaling", "orderDetails.payments.projectAndDepartment.title.projectOnly": "Sett prosjekt for betaling", "orderDetails.payments.projectAndDepartment.project.placeholder": "<PERSON><PERSON><PERSON> etter prosjekt i regnskap", "orderDetails.payments.projectAndDepartment.project.label": "Prosjekt", "orderDetails.payments.projectAndDepartment.department.placeholder": "S<PERSON>k etter avdeling i regnskap", "orderDetails.payments.projectAndDepartment.department.label": "Avdeling", "orderDetails.payments.subscriptionEnabled": "Auto-trekk tilgjengelig", "orderDetails.payments.comment": "Kommentar", "orderDetails.payments.invoiceNotSent": "<PERSON><PERSON> må sendes først", "orderDetails.payments.paymentReminder": "Betalingspåminnelse", "orderDetails.payments.paymentReminder.disabledToolTip": "Kan ikke skrus på igjen når betaling er sendt", "orderDetails.payments.nextPaymentReminder": "Neste betalingspåminnelse", "orderDetails.payments.sentAt": "<PERSON><PERSON> sendt", "orderDetails.payments.list.title": "<PERSON><PERSON>", "orderDetails.payments.list.paymentStatus": "Status", "orderDetails.payments.list.totalAmount": "<PERSON><PERSON><PERSON>", "orderDetails.payments.list.createdAt": "Opprettet", "orderDetails.payments.list.sentAt": "Send<PERSON>", "orderDetails.payments.list.paidAt": "<PERSON><PERSON> dato", "orderDetails.payments.list.paid": "<PERSON>lt", "orderDetails.payments.list.autoSendAt": "Sendes automatisk", "orderDetails.payments.list.autoSendAtButSent": "<PERSON>ling er sendt, plan<PERSON><PERSON> sending var", "orderDetails.createWorkOrder": "<PERSON><PERSON><PERSON><PERSON>b", "orderDetails.createRepeatingWorkOrder": "Op<PERSON>rett repeterende jobb", "orderDetails.createRepeatingPayment": "Opprett repeterende betaling", "orderDetails.createPayment": "<PERSON><PERSON><PERSON><PERSON> enkel-betaling", "orderDetails.paymentStatus5WithRepeatingPayment": "Repeterende betaling", "order.orderDetails.customer.title": "Kunde", "order.orderDetails.customer.changeCustomer": "<PERSON><PERSON> kunde", "order.orderDetails.customer.setCustomer": "Velg kunde", "order.orderDetails.customer.paymentRecipient": "Betalingsmottaker", "order.orderDetails.customer.addAffiliateContact": "Legg til bedriftskontakt", "order.orderDetails.customer.setPaymentRecipient": "<PERSON>t annen betalings<PERSON>", "order.orderDetails.customer.addAffiliateContactModal.title": "Velg bedriftskontakt", "order.orderDetails.customer.addAffiliateContactModal.searchPlaceholder": "Velg bedriftskontakt (valgfritt)", "order.orderDetails.customer.addAffiliateContactModal.affiliateContact": "Bedriftskontakt", "order.orderDetails.customer.addAffiliateContactModal.createNew": "Opprett ny bedriftskontakt", "order.orderDetails.customer.changeAffiliateContact": "<PERSON><PERSON>", "order.orderDetails.customer.serviceRecipient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "order.orderDetails.customer.noServiceRecipient": "Tjenestemottaker er ikke oppgitt", "order.orderDetails.customer.removeServiceRecipientModalInfo": "Dette vil fjerne den nåværende tjenestemottakeren og sette betalingsmottakeren som mottaker av både betaling og tjeneste", "order.orderDetails.customer.paymentMethod": "Betalingsmetode", "order.orderDetails.customer.paymentMethod.selectPayment": "Standard", "order.orderDetails.customer.paymentMethod.paymentChosen": "er for<PERSON><PERSON><PERSON><PERSON><PERSON> for denne ordren", "order.orderDetails.customer.paymentMethod.dueDate": "Forfallsdato", "order.orderDetails.customer.paymentMethod.dueDate.in": "Om", "order.orderDetails.customer.paymentMethod.dueDate.for": "For", "order.orderDetails.customer.paymentMethod.dueDate.ago": "siden", "order.orderDetails.customer.paymentMethod.days": "dager", "order.orderDetails.customer.paymentMethod.sendAs": "Send som", "order.orderDetails.customer.paymentMethod.sentAs": "<PERSON>t som", "orderDetails.customer.editCustomer.title": "Rediger kunde", "order.orderDetails.customer.partner": "Partner", "order.orderDetails.customer.partner.selectPartner": "<PERSON><PERSON><PERSON> en partner for denne ordren", "order.orderDetails.customer.partner.partnerChosen": "er valgt for denne ordren", "order.orderDetails.customer.partner.contact": "Kontakt", "orderDetails.jobAddress.title": "<PERSON><PERSON><PERSON>", "orderDetails.jobAddress.modal.title": "Rediger addresse", "orderDetails.jobAddress.streetview.title": "Gatevisning", "orderDetails.jobAddress.streetview.openStreetview": "Gatevisning", "orderDetails.jobAddress.streetview.notAvailable": "Gatevisning er ikke tilgjengelig", "orderDetails.customerFeedback.title": "Spørreskjema", "orderDetails.customerFeedback.questionnaire": "Spørreskjema", "orderDetails.customerFeedback.questionnaire.creatQuestion": "<PERSON><PERSON><PERSON><PERSON>", "orderDetails.customerFeedback.questionnaire.notAnswered": "Kunden vil kunne svare på spørsmålene i kundeportalen", "orderDetails.customerFeedback.questionnaire.Answered": "<PERSON><PERSON> har svart på spørreskjema", "orderDetails.customerFeedback.questionnaire.button": "Klikk for å se svarene", "orderDetails.customerFeedback.questionnaire.result": "Spørreskjema", "orderDetails.customerFeedback.questionnaire.modal.title": "Spørreskjema", "orderDetails.customerFeedback.questionnaire.modal.header": "Spørreskjema", "orderDetails.customerFeedback.questionnaire.clickHere": "<PERSON><PERSON><PERSON> her for å opprette en mal", "orderDetails.customerFeedback.questionnaire.noTemplate": "Du har ingen spø<PERSON><PERSON><PERSON><PERSON> maler", "orderDetails.customerFeedback.questionnaire.selectTemplate": "<PERSON><PERSON><PERSON>ø<PERSON>-maler", "orderDetails.customerFeedback.questionnaire.selectTemplate.text": "Velg spørsmål du ønsker å inkludere i spørreskjemaet", "orderDetails.customerFeedback.CustomerRating": "Anmeldelse", "orderDetails.customerFeedback.CustomerRating.notRated": "Ingen tilbakemelding gitt av kunden", "orderDetails.customerFeedback.CustomerRating.customerHasNotRatedYet": "<PERSON>nden har ikke svart enda", "orderDetails.customerFeedback.questionnaire.selectTemplates": "<PERSON><PERSON><PERSON>", "orderDetails.customerFeedback.questionnaire.noTemplatesAvailable": "Ingen maler til<PERSON>ge.", "orderDetails.customerFeedback.questionnaire.addSelectedTemplates": "<PERSON>gg til valgte maler", "orderDetails.customerFeedback.questionnaire.orderCustomerQuestions": "Spørreskjema", "orderDetails.customerFeedback.questionnaire.choices": "<PERSON><PERSON>", "orderDetails.customerFeedback.questionnaire.verifySetChoiceModal.title": "Ønsker du å svare for kunden?", "orderDetails.customerFeedback.questionnaire.customerInput": "<PERSON><PERSON><PERSON> svar", "orderDetails.customerFeedback.questionnaire.createCq": "<PERSON><PERSON><PERSON><PERSON>", "orderDetails.customerFeedback.questionnaire.addMoreTemplatesLabel": "Legg til fra mal", "orderDetails.customerFeedback.questionnaire.selectTemplatePlaceholder": "Velg en mal", "orderDetails.customerFeedback.questionnaire.addButton": "Legg til", "orderDetails.customerFeedback.questionnaire.deleteButton": "<PERSON><PERSON>", "orderDetails.crewChecklist.newTask.label": "Nytt sjekklistepunkt", "orderDetails.crewChecklist.newTask.taskName.placeholder": "Beskrivelse av sjekklistepunkt", "orderDetails.crewChecklist.newTask.taskComment.placeholder": "Kommentar (valgfritt)", "orderDetails.crewChecklist.newTask.button": "Legg til sjekklistepunkt", "orderDetails.crewChecklist.newTaskGroup.label": "Ny sjekkliste-kategori", "orderDetails.crewChecklist.newTaskGroup.placeholder": "Skriv inn kategorinavn", "orderDetails.crewChecklist.newTaskGroup.button": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>kkliste-kategori", "orderDetails.crewChecklist.fromTemplate.placeholder": "Legg til sjekkliste fra mal", "orderDetails.crewChecklist.modal.header": "Sjekkliste", "orderDetails.crewChecklist.modal.addChecklist": "Legg til sjekkliste", "orderDetails.crewChecklist.modal.noTemplates": "<PERSON><PERSON> sjekkliste maler", "orderDetails.crewChecklist.modal.generateTemplate": "Lag ny sjekkliste mal", "orderDetails.crewChecklist.modal.addChecklistTemplate": "Legg til sjekklistemaler", "productDetails.crewChecklist.reportedDeviation": "Kommentar", "productDetails.crewChecklist.noTasks": "<PERSON>gen sjekklister er lagt til", "orderDetails.crewChecklist.selectWorkOrder": "Velg en jobb for å se sjekkliste", "orderDetails.crewChecklist.finished": "<PERSON><PERSON><PERSON><PERSON> jobber", "orderDetails.crewChecklist.upcoming": "Kommende jobber", "orderDetails.crewChecklist.noUpcomingWorkOrders": "<PERSON>ne ordren har ingen kommende jobber", "orderDetails.crewChecklist.noFinishedWorkOrders": "<PERSON><PERSON> ordren har ingen fullførte jobber", "orderDetails.crewChecklist.title": "Sjekkliste for ansatte", "orderDetails.crewChecklist.addChecklist": "Legg til sjekkliste", "orderDetails.crewChecklist.checklistOngoing": "Teamet har begynt å sjekke av elementer i sjekklisten", "orderDetails.crewChecklist.checklistFinished": "Sjekklisten er ferdig!", "orderDetails.crewChecklist.notStarted": "Teamet har ennå ikke begynt å sjekke av oppgaver sjekklisten", "orderDetails.crewChecklist.repeating": "Her kan du styre sjekklistene som benyttes av den repeterende jobben din", "orderDetails.crewChecklist.checklistButton": "Se <PERSON>le sjekklisten", "productDetails.crewChecklist.addTemplate": "Legg til mal", "productDetails.crewChecklist.noTemplate": "Ingen maler", "orderDetails.timetracking.title": "Tidsregistrering", "orderDetails.timetracking.convertTrackedTime": "<PERSON><PERSON><PERSON> ført tid", "orderDetails.timetracking.convertTrackedTimeTooltip": "Aktiver denne for at de ansattes lønnsgrunnlag skal defineres av tiden de har ført selv (sjekk inn og sjekk ut). Når denne er deaktivert vil lønnsgrunnlaget defineres av oppdragets planlagte varighet.", "orderDetails.timetracking.btn.job": "<PERSON><PERSON>", "orderDetails.timetracking.btn.crew": "Crew", "orderDetails.timetracking.job.start": "Start", "orderDetails.timetracking.job.end": "Slutt", "orderDetails.timetracking.job.Total": "Totalt", "orderDetails.timetracking.job.noData": "Ingen registrering", "orderDetails.timetracking.modal.title": "Innstillinger for timesføring", "orderDetails.timetracking.modal.activity.order": "Velg standard aktivitet for hele ordren", "orderDetails.timetracking.modal.activity.workOrder": "Velg aktivitet for jobben", "orderDetails.timetracking.modal.activity.transportMinutes.activate": "Legg til automatisk timesføring for transport", "orderDetails.timetracking.modal.activity.transportMinutes.activate.description": "H<PERSON> du aktiverer denne kan du angi et fast antall minutter føres som en egen timesføring for hver ansatt som er tildelt denne jobben. Timesføringen vil opprettes når jobben fullføres.", "orderDetails.timetracking.modal.activity.transportMinutes.activate.disabled": "Du har ikke valgt noen standard aktivitet for transport. Dette kan gjøres under Innstillinger -> <PERSON><PERSON><PERSON>", "orderDetails.timetracking.modal.activity.transportMinutes": "<PERSON><PERSON><PERSON> minutter transporttid", "orderDetails.timetracking.modal.pause.toggleLabel": "Legg til pause automatisk", "orderDetails.timetracking.modal.pause.infoHelp": "<PERSON><PERSON><PERSON> du legger til pause automatisk vil det bli legges til en pause på slutten av jobbens timesføringer for alle ansatte som er tildelt på jobben. Du kan oppgi et spesifikt tidspunkt for pausens start hvis du ønsker et gitt tidsrom for pausen.", "orderDetails.timetracking.modal.pause.duration": "<PERSON><PERSON><PERSON> varighet", "orderDetails.timetracking.modal.pause.start": "Starttid for pause (valg<PERSON>ritt)", "orderDetails.timeline.by": "av", "orderDetails.timeline.today": "I dag", "orderDetails.timeline.opened": "Opened", "orderDetails.timeline.notOpened": "Not opened", "orderDetails.notes.title": "Notater", "orderDetails.notes.visibleToCustomer": "Synlig for kunde", "orderDetails.notes.addNote": "Legg til", "orderDetails.notes.unconfirmedWarning.title": "Kunden vil ikke varsles", "orderDetails.notes.unconfirmedWarning.bodyRegular": "Kunden vil ikke motta varsel per e-post om ny melding så lenge ordren er ubekreftet og/eller tilbud ikke er sendt.", "orderDetails.notes.tooltip": "Synlig for kunde", "orderDetails.notes.daysAgo": "dager siden", "orderDetails.notes.deleted": "<PERSON><PERSON><PERSON>", "orderDetails.notes.description": "Vennligst legg til notater", "orderDetails.notes.editNote": "Endre notat/melding", "orderDetails.notes.hoursAgo": "timer siden", "orderDetails.notes.internal": "Intern", "orderDetails.notes.external": "Synlig for kunde", "orderDetails.notes.internalNote": "Internt notat", "orderDetails.notes.justNow": "nå nylig", "orderDetails.notes.messageDeletedBy": "Melding slettet av", "orderDetails.notes.minutesAgo": "minutter siden", "orderDetails.notes.secondsAgo": "sekunder siden", "orderDetails.notes.updated": "Oppdatert", "orderDetails.notes.visibleToCustomerToolTip": "Notater merket som 'Synlig for kunde' vil bli vist for kunden. Umerkede notater er kun synlige for selskapets ansatte", "orderDetails.notes.tabs.customer": "Kundemeldinger", "orderDetails.notes.tabs.internal": "Interne notater", "orderDetails.notifications.title": "Kundenotifikasjoner", "orderDetails.notifications.emailToggle": "Email", "orderDetails.notifications.smsToggle": "Automatiske kundemeldinger", "orderDetails.reports.title": "Crew-rapporter", "orderDetails.attachments.title": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderDetails.attachments.visibleToCustomer": "Tilgjengelig for kunde", "orders.orderDetails.attachments.visibleToCrew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for crew", "orderDetails.reports.total": "Totalt", "orderDetails.reports.attachmentsAdded": "elementer lagt til", "orderDetails.reports.btn": "Se alle bilder og dokumenter", "orderDetails.reports.modal.title": "Alle bilder og dokumenter", "orderDetails.timeline.title": "Tidslinje", "orderDetails.timeline.all": "Alle", "orderDetails.timeline.filterBy": "<PERSON><PERSON><PERSON> etter", "orderDetails.timeline.noData": "Ingen data tilgjengelig", "orderDetails.projectsAndDepartments.title": "Prosjekt og avdeling", "orderDetails.projectsAndDepartments.title.projectOnly": "Prosjekt", "orderDetails.projectsAndDepartments.project": "Prosjekt", "orderDetails.projectsAndDepartments.project.placeholder": "<PERSON><PERSON><PERSON> etter prosjekt fra regnskap", "orderDetails.projectsAndDepartments.department": "Avdeling", "orderDetails.projectsAndDepartments.department.placeholder": "<PERSON><PERSON><PERSON> etter avdeling fra regnskap", "orderDetails.addresses.multiModal.boldKey": "Dette vil legge til adressen til alle jobber i denne ordren.", "orderDetails.addresses.multiModal.regularKey": "Er du sikker på at du vil fortsette?", "orderDetails.addresses.deleteModal.multiple.boldKey": "Denne adressen er i bruk på flere jobber i denne ordren. <PERSON><PERSON> du sletter den vil den bli fjernet fra alle jobbene.", "orderDetails.addresses.deleteModal.multiple.regularKey": "Er du sikker på at du vil fortsette?", "orderDetails.addresses.multipleWorkOrders": "Flere jobber", "orderDetails.orderLines.title": "<PERSON><PERSON><PERSON><PERSON>", "orderDetails.orderLines.pricesIncVat": "P<PERSON>r inkludert MVA", "orderDetails.orderLines.pricesExVat": "Priser ekskludert MVA", "orderDetails.orderLines.createPayment": "Send til betaling", "orderDetails.orderLines.description": "Beskrivelse", "orderDetails.orderLines.quantity": "<PERSON><PERSON><PERSON>", "orderDetails.orderLines.unitPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orderDetails.orderLines.discount": "<PERSON><PERSON><PERSON>", "orderDetails.orderLines.discount.modal.title": "<PERSON><PERSON> rabatt", "orderDetails.orderLines.discount.modal.label": "<PERSON><PERSON> rabatt for ordrelinjer som er markert", "orderDetails.orderLines.total": "Totalt", "orderDetails.orderLines.status": "Status", "orderDetails.orderLines.unit": "<PERSON><PERSON><PERSON>", "orderDetails.orderLines.vat": "MVA", "orderDetails.orderLines.chooseProduct": "Velg produkt (valgfritt)", "orderDetails.orderLines.paymentStatusTooltip": "<PERSON>ne ordre<PERSON>jen er ikke lagt til ved noen betaling enda.", "orderDetails.orderLines.lockedTooltip": "Denne ordrelinjen er del av en betaling og er derfor låst. Hvis betalingen ikke er sendt kan du endre ordrelinjen i betalingen, klikk på låsen for å åpne.", "orderDetails.orderLines.productLinkedTooltip1": "<PERSON><PERSON> ordrelinjen er koblet til produktet", "orderDetails.orderLines.productLinkedTooltip2": "Klikk for gå til produktet", "orderDetails.orderLines.addOrderLine": "Legg til ordrelinje", "orderDetails.orderLines.disabledForUnSavedTemplate": "<PERSON>gre malen før du legger til ordrelinjer", "orderDetails.orderLines.addPriceRule": "<PERSON><PERSON> til p<PERSON>regel", "orderDetails.orderLines.onlyOneManualPriceRuleAllowed": "Kun én produktvariant er tillatt per ordrelinje", "orderDetails.orderLines.noManualPriceRulesAvailable": "Ingen produktvarianter tilgjengelig", "orderDetails.orderLines.addPriceRulePlaceholder": "Velg produktvariant", "orderDetails.orderLines.disabledPriceRuleDescription": "<PERSON><PERSON><PERSON> alle beløpsbaserte prisregler for å legge til denne", "orderDetails.orderLines.totalExVat": "Totalt eks. MVA", "orderDetails.orderLines.paymentDisabledTooltip.noPaymentRecipient": "Velg en kunde før du sender til betaling", "orderDetails.orderLines.paymentDisabledTooltip.noPaymentRecipientAndNotConfirmed": "Ordren må bekreftes og kunde må velges før du sender til betaling", "orderDetails.orderLines.paymentDisabledTooltip.notConfirmed": "Ordren må bekreftes før du sender til betaling", "orderDetails.orderLines.paymentDisabledTooltip.noOrderLines": "Ingen ordrelinjer er tilgjengelige for betalingsopprettelse", "orderDetails.orderLines.paymentDisabledTooltip.orderCancelled": "Ordren er kansellert", "orderDetails.orderLines.paymentDisabledTooltip.workOrderHasPayment": "Denne jobben har allerede en betaling", "orderDetails.orderLines.addedByCustomerTooltip.creation": "<PERSON><PERSON> ordre<PERSON>jen ble lagt til av kunden når ordren ble opprettet", "orderDetails.orderLines.addedByCustomerTooltip.updated": "<PERSON>ne ordrelin<PERSON> ble lagt til av kunden i kundeportalen", "orderDetails.orderLines.deleteChecked": "<PERSON><PERSON> valgte ordrelinjer", "orderDetails.orderLines.connectToWorkOrder": "<PERSON><PERSON> til jobb", "orderDetails.orderLines.connectToWorkOrder.disconnect": "<PERSON><PERSON> fra jobb", "orderDetails.orderLines.connectToWorkOrder.select": "<PERSON><PERSON><PERSON>b", "workOrder.deletePaymentModal.titleTranslationKey": "Slette til<PERSON>ørende betaling?", "workOrder.deletePaymentModal.bodyRegularTranslationKey": "Det er opprettet en betaling tilknyttet denne jobben.", "workOrder.deletePaymentModal.bodyRegularSecondaryTranslationKey": "Ønsker du å slette denne betalingen sammen med jobben?", "workOrder.deleteChildrenModal.titleTranslationKey": "<PERSON><PERSON> ikke-p<PERSON><PERSON><PERSON><PERSON> jobber?", "workOrder.deleteChildrenModal.bodyRegularTranslationKey": "Denne serien har opprettet flere jobber som ikke er påbegynt enda.", "workOrder.deleteChildrenModal.bodyRegularSecondaryTranslationKey": "Ønsker du å slette disse jobbene sammen med denne serien?", "workOrder.finishModal.title": "<PERSON>r du sikker?", "workOrder.finishModal.confirmationMessage": "<PERSON><PERSON>en vil fullføre de valgte jobbene. For ordrelinjer med tidsregistrering aktivert vil det registrerte antallet brukes automatisk.", "workOrder.list.title": "<PERSON><PERSON>", "workOrder.list.column.title": "<PERSON><PERSON><PERSON>", "workOrder.list.column.status": "Status", "workOrder.list.column.customer": "Kunde", "workOrder.list.column.paymentStatus": "<PERSON>lings<PERSON><PERSON>", "workOrder.list.column.date": "Da<PERSON>", "workOrder.list.column.time": "Tid", "workOrder.list.column.assignees": "Tildelte ansatte", "workOrder.list.column.subcontractor": "Underleverandør/Oppdragsgiver", "workOrder.list.receivedFrom": "Mottatt fra", "workOrder.list.column.address": "<PERSON><PERSON><PERSON>", "workOrder.list.showAll": "Vis alle jobber", "workOrder.list.next5": "Neste 5 kommende jobber", "workOrder.list.deleteSelected": "<PERSON><PERSON> valgte jobber", "workOrder.list.modal.title": "Jobb for", "workOrder.list.filter.status": "Status", "workOrder.list.filter.execution": "Utførelsesdato", "workOrder.list.filter.employees": "Ansatte", "workOrder.list.filter.resources": "<PERSON><PERSON><PERSON><PERSON>", "workOrder.list.filter.received": "<PERSON><PERSON><PERSON> som underleverandør", "workOrder.list.filter.sent": "Sendt til underleverandør", "workOrder.list.filter.unplanned": "Ikke <PERSON>", "workOrder.list.sorting.created": "Opprettelsesdato", "workOrder.list.delete": "<PERSON><PERSON>", "workOrder.list.finish": "<PERSON><PERSON><PERSON><PERSON>", "workOrder.receivedList.column.contractingAuthority": "Oppdragsgiver", "workOrder.receivedList.column.receivedAt": "<PERSON><PERSON><PERSON>", "workOrder.receivedList.youHaveNotAnswered": "Du har ikke svart", "workOrder.receivedList.accepted": "Godtatt", "workOrder.receivedList.column.declined": "Avslått", "orderDetails.paymentSchedules.title": "Repeterende betaling", "orderDetails.paymentSchedules.title.perWorkOrder": "Repeterende betaling per jobb", "orderDetails.paymentSchedules.noSubscriptionActive": "Kort ikke registrert", "orderDetails.paymentSchedules.autoSendDisabled": "Sendes ikke automatisk", "orderDetails.paymentSchedules.autoSendDisabled.toolTip": "Betalingen vil ikke bli sendt automatisk til kunden når en jobb fullføres. Klikk for å aktivere automatisk sending.", "orderDetails.paymentSchedules.autoSendEnabled": "Sendes automatisk", "orderDetails.paymentSchedules.autoSendEnabled.toolTip": "Betalingen vil bli sendt automatisk til kunden når en jobb fullføres. Klikk for å deaktivere automatisk sending.", "orderDetails.paymentSchedules.autoSendDisabled.consolidated": "Legges ikke til automatisk", "orderDetails.paymentSchedules.autoSendDisabled.toolTip.consolidated": "Betalingen vil ikke bli lagt til aktiv samlefaktura automatisk når en jobb fullføres. Klikk for å aktivere dette.", "orderDetails.paymentSchedules.autoSendEnabled.consolidated": "Legges til automatisk", "orderDetails.paymentSchedules.autoSendEnabled.toolTip.consolidated": "Betalingen vil automatisk bli lagt til den aktive samlefakturaen når en jobb fullføres. Klikk for å deaktivere dette.", "orderDetails.paymentSchedules.nextPayment": "Neste", "orderDetails.paymentSchedules.nextPayment.empty": "Ingen neste betaling", "orderDetails.paymentSchedules.lastPayment": "<PERSON><PERSON><PERSON>", "orderDetails.paymentSchedules.lastPayment.empty": "Ingen tidligere betaling", "orderDetails.paymentSchedules.totalPayments": "Totalt", "orderDetails.paymentSchedules.totalUnpaidSentPayments": "Totalt ubetalte sendte", "orderDetails.paymentSchedules.frequency": "Opprettes automatisk for hver", "orderDetails.paymentSchedules.frequency.sent": "Sendes", "orderDetails.paymentSchedules.active": "Aktiv", "orderDetails.paymentSchedules.deactivated": "Repterende betaling ikke aktivert", "orderDetails.paymentSchedules.addCustomerBeforeActivating": "Legg til en kunde på ordren før du aktiverer", "orderDetails.paymentSchedules.confirmOrderBeforeActivating": "Ordren må være bekreftet før du aktiverer. Når ordren bekreftes vil den repeterende betalingen aktiveres automatisk", "orderDetails.paymentSchedules.activeTooltip": "<PERSON><PERSON> denne er deaktivert, vil ikke nye betalinger opprettes.", "orderDetails.paymentSchedules.startsAfter": "Oppstart fra", "orderDetails.paymentSchedules.stopsAfter": "Avslut<PERSON>", "paymentDetails.title.create": "<PERSON><PERSON><PERSON><PERSON> betaling", "paymentDetails.title.create.repeating": "Opprett repeterende betaling", "paymentDetails.title.update": "<PERSON><PERSON> betaling", "paymentDetails.title.update.repeating": "<PERSON><PERSON> repeterende betaling", "paymentDetails.goToOrder": "<PERSON><PERSON> til ordre", "paymentDetails.createAndSend": "<PERSON><PERSON><PERSON><PERSON> og send", "paymentDetails.saveWithoutSend": "Lagre uten å sende", "paymentDetails.paymentMethodDescription.id3": "Dette betalingsalternativet gir kunden mulighet til å velge betalingsmetode selv. Når betalingen sendes vil kunden motta en SMS/e-post med en lenke til betalingssiden. Her vil kunden kunne velge mellom de betalingsmetodene som er tilgjengelige.", "paymentDetails.paymentMethodDescription.id4": "Dette betalingsalternativet kan benyttes når du ønsker å ta betalt uten å benytte deg av betalingsløsningene i Between. Når betalingen sendes vil den automatisk merkes som betalt, og du må selv sikre at du tar betalt av kunden.", "paymentDetails.paymentMethodDescription.id10": "Opprett faktura og send denne gjennom regnskapssystemet du bruker. Sending av faktura gjøres gjennom Between, og fakturaens betalingsstatus vil oppdateres i Between innen én time etter at fakturaen er betalt.", "paymentDetails.paymentMethodDescription.id14": "Ved å velge denne betalingsmetoden kan kunden registrere sitt kredittkort, og alle betalinger opprettet av denne repeterende betalingen vil så bli automatisk trukket når den enkelte betaling sendes.", "paymentDetails.paymentMethodDescription.id15": "Denne betalingsmetoden vil legge til denne betalingen i den valgte samlefakturaen.", "paymentDetails.repeatingTypeTooltip.fixed": "En fast repeterende betaling vil automatisk opprette betalinger med et gitt intervall, uavhengig hvilket oppsett du har for planlagte jobber.", "paymentDetails.repeatingTypeTooltip.perJob": "Repeterende betaling per jobb vil automatisk opprette en ny betaling hver gang en jobb er fullført.", "paymentDetails.selectPaymentMethod": "Betalingsmetode", "paymentDetails.selectPaymentMethod.noCustomer": "Velg en kunde for å gjøre faktura tilgjengelig", "paymentDetails.selectPaymentMethod.consolidatedDisabled": "Samlefaktura er ikke aktivert for denne kunden", "paymentDetails.selectPaymentMethod.consolidatedDisabledBecauseInvoice": "Reverser betalingens regnskapsføring for å velge samlefaktura", "paymentDetails.selectPaymentMethod.invoiceDisabledBecausePushed": "Reverser betalingens regnskapsføring for å velge faktura", "paymentDetails.selectPaymentMethod.disabledDeleteTooltip": "<PERSON><PERSON><PERSON> regnskapssynkronisering for denne betalingen før du sletter den.", "paymentDetails.saveWithoutSend.create": "Lagre uten å sende", "paymentDetails.saveAndSend.create": "Send til betaling", "paymentDetails.saveWithoutSend.update": "Lagre", "paymentDetails.saveAndSend.update": "Send betaling", "paymentDetails.saveAndSend.update.again": "Send på nytt", "paymentDetails.paymentReminders": "Betalingspåminnelser", "paymentDetails.paymentReminders.tooltip": "<PERSON>vis denne er aktivert vil betalingspåminnelser sendes i henhold til varslingsinnstillingene deres. Hvis denne deaktiveres kan den ikke aktiveres igjen etter at betalingen er sendt.", "paymentDetails.invoiceSettings": "Fakturainnstillinger", "paymentDetails.invoiceSettings.invoiceSendType": "Fakturatype", "paymentDetails.downloadInvoicePDF": "Last ned faktura som PDF", "paymentDetails.sendReceipt": "Send kvittering", "paymentDetails.tabs.single": "Enkel betaling", "paymentDetails.tabs.repeating": "Per jobb", "paymentDetails.tabs.fixed": "Fast repeterende betaling", "paymentDetails.infoBox.repeatingPayment": "En repeterende betaling er en betaling som vil gjentas for valgte jobber. Antall betalinger som opprettes i forkant vil følge reglene for den valgte repeterende jobben.", "paymentDetails.infoBox.fixedRepeatingPayment": "En fast repeterende betaling vil automatisk opprette betalinger med et gitt intervall.", "paymentDetails.selectWorkOrder": "Velg repeterende jobb", "paymentDetails.workOrder": "<PERSON><PERSON><PERSON><PERSON> jobb", "paymentDetails.subscriptionPaymentMethodName": "Automatisk trekk (Quickpay)", "paymentDetails.repeatingType": "<PERSON><PERSON>dan ønsker du å ta betalt?", "paymentDetails.dayOfMonth": "<PERSON><PERSON><PERSON> ønsker du å sende ut betaling/faktura?", "paymentDetails.dayOfMonth.first": "Den første dagen i måneden", "paymentDetails.dayOfMonth.last": "Den siste dagen i måneden", "paymentDetails.firstPayment": "<PERSON><PERSON>r skal første betaling/faktura sendes?", "paymentDetails.firstPayment.disabled": "<PERSON><PERSON><PERSON><PERSON> mulige dato", "paymentDetails.lastPayment": "<PERSON><PERSON><PERSON><PERSON>", "paymentDetails.lastPayment.disabled": "Ingen stoppdato", "paymentDetails.createRepeatingPayment": "Opprett repeterende betaling", "paymentDetails.beforeFirstPayment": "<PERSON><PERSON><PERSON> fø<PERSON>", "paymentDetails.afterLastPayment": "<PERSON>tter siste betaling", "paymentDetails.firstPaymentOn": "<PERSON><PERSON><PERSON><PERSON>", "paymentDetails.lastPaymentOn": "<PERSON><PERSON> betaling", "paymentDetails.totalPayments": "Totalt", "paymentDetails.fixedPaymentStopUnitType.never": "<PERSON>gen gitt varighet", "paymentDetails.fixedPaymentStopUnitType.weeks": "<PERSON><PERSON>(r)", "paymentDetails.fixedPaymentStopUnitType.months": "<PERSON><PERSON><PERSON>(er)", "paymentDetails.fixedPaymentStopUnitType.years": "<PERSON><PERSON>", "paymentDetails.repeatingPlaceholderToggleDisabledTooltip.noCustomer": "Du må velge en kunde for ordren før du kan opprette den repeterende betalingen", "paymentDetails.repeatingPlaceholderToggleDisabledTooltip.noOrderLines": "Du må legge til minst én ordrelinje før du kan opprette den repeterende betalingen", "paymentDetails.repeatingPlaceholderToggleDisabledTooltip.noSchedule": "Du må velge en repeterende jobb før du kan opprette den repeterende betalingen", "paymentDetails.cannotSendPm14": "Kan ikke sende betaling når betalingsmetode er auto-trekk", "paymentDetails.noOrderLinesSelected": "Ingen ordrelinjer valgt", "paymentDetails.noPaymentMethodSelected": "Ingen betalingsmetode valgt", "paymentDetails.alreadySent": "Betalingen er allerede sendt", "paymentDetails.noConsolidatedInvoiceSelected": "Velg samlefakturaoppsett før du lagrer", "paymentDetails.noInvoiceSendTypeSelected": "Velg fakturatype før du sender til betaling", "paymentDetails.invalidInvoiceEmail": "Ugyldig e-postadresse", "paymentDetails.invalidDueDate": "Ugyldig forfallsdato", "paymentDetails.consInvNoOrderLinesSelected": "Samlefakturaen inneholder ingen ordre<PERSON>jer, eller <PERSON> er null", "paymentDetails.consolidatedInvoice.title": "Oppsett for samlefaktura", "paymentDetails.consolidatedInvoice.name": "Navn på samlefaktura", "paymentDetails.consolidatedInvoice.name.tooltip": "Oppgi et gjenkjenbart navn på samlefakturaen", "paymentDetails.consolidatedInvoice.autoSend": "Send automatisk", "paymentDetails.consolidatedInvoice.autoSend.tooltip": "<PERSON>elg dette alternativet for at samlefakturaen skal sendes automatisk ved gitte intervaller", "paymentDetails.consolidatedInvoice.sendManually": "Send manuelt", "paymentDetails.consolidatedInvoice.sendManually.tooltip": "<PERSON><PERSON>g dette alternativet når du ønsker å sende samlefakturaen manuelt", "paymentDetails.consolidatedInvoice.selectInvoice": "<PERSON><PERSON>g oppsett for samlefaktura", "paymentDetails.consolidatedInvoice.createNew": "Opprett nytt oppsett for samlefaktura", "paymentDetails.consolidatedInvoice.placeholder": "Velg eller opprett et oppsett for samlefaktura", "payments.list.title": "<PERSON><PERSON>", "payments.list.createdAt": "Opprettet", "payments.list.totalAmount": "<PERSON>t beløp", "payments.list.capturedAt": "<PERSON>lt", "payments.list.status": "<PERSON>lings<PERSON><PERSON>", "payments.list.paymentMethod": "Betalingsmetode", "payments.list.autoSendAt": "Sendes automatisk", "payments.list.paymentSentAt": "<PERSON><PERSON> sendt", "payments.list.customer": "Kunde", "payments.list.products": "<PERSON>du<PERSON><PERSON>", "payments.list.paymentStatus.failed": "Betaling feilet", "payments.list.sorting.created_at": "Oppret<PERSON><PERSON> dato", "payments.list.sorting.sent": "<PERSON>t dato", "payments.list.sorting.captured": "<PERSON><PERSON> dato", "payments.list.sorting.amount": "<PERSON><PERSON><PERSON>", "workOrderDetails.workOrderTitleAndDescription": "Tittel og beskrivelse", "workOrderDetails.workOrderTitle": "<PERSON><PERSON><PERSON>", "workOrderDetails.workOrderDescription": "Beskrivelse", "workOrderDetails.workOrderDescription.showMore": "Vis mer", "workOrderDetails.workOrderDescription.showLess": "Vis mindre", "workOrderDetails.workOrderTitlePlaceholder": "Tittel for jobb...", "workOrderDetails.workOrderDescriptionPlaceholder": "Arbeidsbeskrivelse...", "workOrderDetails.subcontractor.title": "Underleverandør", "workOrderDetails.subcontractor.accepted": "Akseptert av", "workOrderDetails.subcontractor.declined": "Avslått av", "workOrderDetails.subcontractor.declined.reason": "Årsak", "workOrderDetails.subcontractor.declined.noReason": "<PERSON><PERSON><PERSON>", "workOrderDetails.subcontractor.pending": "Venter på svar", "workOrderDetails.dateAndTime.title": "<PERSON>to og tid", "workOrderDetails.dateAndTime.arrival": "Ankomstvindu", "workOrderDetails.dateAndTime.arrivalTooltip": "Angi et ankomstvindu for å gi kunden en idé om når jobben vil starte", "workOrderDetails.open": "<PERSON><PERSON><PERSON>", "workOrderDetails.createWithoutOrderLines": "<PERSON><PERSON><PERSON><PERSON> uten or<PERSON>", "workOrderDetails.createWithoutOrderLines.tooltip": "Jobbmalen du har valgt inneholder ordrelinjer. Bruk denne knappen for å opprette jobben uten ordrelinjene fra malen.", "workOrderDetails.contractor.asCompany.title": "Sendt til underleverandør", "workOrderDetails.contractor.asCompany.description": "<PERSON><PERSON> jobben har blitt sendt til følgende underleverandør", "workOrderDetails.contractor.asCompany.pending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> har ikke svart på forespørselen enda", "workOrderDetails.contractor.title": "Underleverandørvisning", "workOrderDetails.contractor.viewingAs": "Du ser denne jobben som underleverandør.", "workOrderDetails.contractor.owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "workOrderDetails.contractor.ownerContact": "Tildelt av", "workOrderDetails.contractor.acceptedBy": "Akseptert av", "workOrderDetails.contractor.declinedBy": "Avslått av", "workOrderDetails.contractor.declinedMessage": "<PERSON><PERSON> selskap har av<PERSON><PERSON><PERSON><PERSON> denne jobben, og er derfor ikke tilgjengelig for endring.", "workOrderDetails.contractor.pending": "Kontrakten er ikke blitt besvart av ditt selskap enda", "workOrderDetails.contractor.accept": "<PERSON><PERSON><PERSON><PERSON>", "workOrderDetails.contractor.decline": "Avslå", "workOrderDetails.contractor.remove": "<PERSON><PERSON><PERSON><PERSON>", "workOrderDetails.pastDateModal.title": "Sette utførelsesdato bakover i tid?", "workOrderDetails.pastDateModal.bodyBold": "Du har satt en utførelsesdato som er tidligere enn dagens dato.", "workOrderDetails.pastDateModal.bodyRegular": "Er du sikker på at du vil fortsette?", "workOrderDetails.modal.create.title": "<PERSON><PERSON><PERSON><PERSON>b", "workOrderDetails.modal.create.createNoExecution": "<PERSON><PERSON><PERSON><PERSON> uten tidspunkt", "workOrderDetails.goToOrder": "<PERSON><PERSON> til ordre", "workOrderDetails.card.noDateSet": "Tidspunkt ikke satt", "workOrderDetails.card.setDate": "<PERSON>t dato", "workOrderDetails.createFromTemplate": "Maler for jobber", "workOrderDetails.createFromTemplate.showAll": "Vis alle", "workOrderDetails.createFromTemplate.noTemplates.title": "Tips", "workOrderDetails.createFromTemplate.noTemplates.description": "<PERSON><PERSON><PERSON><PERSON> jobbmaler for å hurtig kunne opprette en ordre med en forhåndsdefinert jobb.", "workOrderDetails.actionButtons.duplicate": "<PERSON><PERSON><PERSON><PERSON> jobb", "workOrderDetails.actionButtons.finish": "<PERSON><PERSON><PERSON><PERSON>", "workOrderDetails.finishBeforeConfirmedModal.title": "Ordre er ikke bekreftet", "workOrderDetails.finishBeforeConfirmedModal.bodyBold": "<PERSON><PERSON><PERSON> denne jobben tilhører er ikke bekreftet enda.", "workOrderDetails.finishBeforeConfirmedModal.bodyRegular": "Er du sikker på at du vil fullføre jobben?", "workOrderDetails.duplicateModal.title": "<PERSON><PERSON><PERSON><PERSON> jobb", "workOrderDetails.duplicateModal.chosenDates": "<PERSON><PERSON><PERSON> da<PERSON><PERSON>", "workOrderDetails.duplicateModal.users": "<PERSON><PERSON><PERSON> til<PERSON> ansatte", "workOrderDetails.duplicateModal.resources": "<PERSON><PERSON><PERSON> til<PERSON> ressurser", "workOrderDetails.duplicateModal.checklists": "<PERSON><PERSON><PERSON>", "workOrderDetails.duplicateModal.orderLines": "<PERSON><PERSON><PERSON>", "workOrderDetails.duplicateModal.customerQuestions": "<PERSON><PERSON><PERSON>", "workOrderDetails.quantityProposalModal.title": "Forslag til ordrelinje-antall", "workOrderDetails.quantityProposalModal.description": "<PERSON>rt på tiden som er ført på denne jobben har vi følgende forslag til endring av ordrelinjer", "workOrderDetails.quantityProposalModal.close": "<PERSON><PERSON><PERSON><PERSON> uten forslagsendring", "workOrderDetails.repeatingView.infoBox.title": "Du ser på oppsettet for repeterende jobb.", "workOrderDetails.repeatingView.infoBox.description": "H<PERSON> du ønsker å gjøre endringer på en enkelt jobb må du åpne den spesifikke jobben du ønsker å endre.", "workOrderDetails.repeatingView.updateChildrenModal.title": "<PERSON>p<PERSON><PERSON> eksister<PERSON>e jobber", "workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey": "<PERSON>ne serien har fremtidige ikke-påbegynte jobber.", "workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey": "<PERSON><PERSON> du velger nei, vil denne endringen kun gjøres gjeldende for jobber som ikke er opprettet enda.", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.executionUpdate": "Ønsker du å oppdatere dato og tid for disse jobbene også?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.addressUpdate": "Ønsker du å oppdatere adresser for disse jobbene også?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.checkListUpdate": "Ønsker du å oppdatere sjekklistene for disse jobbene også?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.assignUpdate": "Ønsker du å legge til den ansatte på disse jobbene også?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.activity": "Ønsker du å endre standard aktivitet for disse jobbene også?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.registrationType": "Ønsker du å endre registreringstypen for disse også?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.unassignUpdate": "Ønsker du å fjerne den ansatte fra disse jobbene også?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.textUpdate": "Ønsker du å oppdatere tittel og beskrivelse for disse jobbene også?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.orderLineCreation": "Ønsker du å legge til denne ordrelinjen til disse jobbene og<PERSON>å?", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.orderLineUpdate": "Ønsker du å oppdatere tilsvarende ordrelinje ved disse jobbene også?", "workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey.repeatingPaymentCreation": "Den valgte serien for denne repeterende betalingen har fremtidige ikke-påbegynte jobber.", "workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.repeatingPaymentCreation": "Ønsker du å legge til denne betalingens ordrelinjer til disse eksisterende jobbene?", "workOrderDetails.repeatingView.title": "Tidspunkt og frekvens", "workOrderDetails.repeatingView.startDate": "Startdato", "workOrderDetails.repeatingView.endDate": "Sluttdato", "workOrderDetails.repeatingView.startTime": "Starter", "workOrderDetails.repeatingView.endTime": "Avslut<PERSON>", "workOrderDetails.repeatingView.estimatedDuration": "<PERSON><PERSON><PERSON><PERSON> varighet", "workOrderDetails.repeatingView.hours": "timer", "workOrderDetails.repeatingView.minutes": "minutter", "workOrderDetails.repeatingView.scheduleButton": "<PERSON><PERSON><PERSON>", "workOrderDetails.repeatingView.scheduleButton.verifySchedule": "Verifiser tid og frekvens", "workOrderDetails.repeatingView.showAllJobs": "<PERSON>is jobber", "workOrderDetails.createOrderView.title": "<PERSON><PERSON><PERSON><PERSON> ordre", "workOrderDetails.createOrderView.createOrder": "<PERSON><PERSON><PERSON><PERSON> ordre", "workOrderDetails.createOrderView.createEmptyOrder": "<PERSON><PERSON><PERSON><PERSON> uten jobb", "workOrderDetails.createOrderView.orderType": "Ordretype", "workOrderDetails.createOrderView.orderType.single": "<PERSON><PERSON>", "workOrderDetails.createOrderView.orderType.repeating": "<PERSON><PERSON><PERSON><PERSON>", "scheduleSetup.startStop.start": "Oppstart", "scheduleSetup.startStop.stop": "Avslut<PERSON>", "scheduleSetup.startStop.immediately": "Start umiddelbart", "scheduleSetup.startStop.never": "<PERSON><PERSON><PERSON>", "orderDetails.noRepeatingWorkOrder": "Denne ordren har ingen repeterende jobber", "orderDetails.noWorkOrder": "<PERSON>ne ordren har ingen jobber", "orderDetails.workOrder": "<PERSON><PERSON>", "orderDetails.workOrderScheduleCard.noExecutionTooltip": "Du må definere et oppstartstidspunkt før du kan opprette jobb", "orderDetails.workOrderCardContainer": "<PERSON><PERSON>", "orderDetails.workOrderCardContainer.button": "<PERSON><PERSON> til jobber", "orderDetails.orderLines.addComment": "Legg til kommentar", "orderDetails.orderLines.addProduct.customProduct": "Egendefinert produkt", "orderDetails.orderLines.edit.title": "Rediger produkter", "orderDetails.orderLines.orderLines.multiple": "<PERSON><PERSON><PERSON><PERSON>", "orderDetails.orderLines.orderLines.single": "ordrelinje", "orderDetails.orderLines.workOrderConnectedTooltip": "<PERSON><PERSON> ordrelinjen er koblet til en jobb, klikk for å åpne", "orderDetails.orderLines.trackTimeTooltip": "Denne ordrelinjen vil bli brukt til å registrere timer brukt på ordren. <PERSON>vis ordrelinjen er koblet til en jobb, vil den kun registrere tid for den spesifikke jobben.", "orderDetails.orderLines.trackTimeLabel.order": "Bruk denne ordrelinjen til å registerere tid som føres på ordren", "orderDetails.orderLines.trackTimeLabel.workOrder": "Bruk denne ordrelinjen til å registerere tid som føres på tilkoblet jobb", "orderDetails.orderLines.trackTimeDisabledTooltip.workOrder": "Jobben som er koblet til denne ordrelinjen har allerede en ordrelinje med tidsregistrering aktivert. Deaktiver denne først.", "orderDetails.orderLines.trackTimeDisabledTooltip.order": "Denne ordren har allerede en ordrelinje med tidsregistrering aktivert. Deaktiver denne først.", "orderDetails.orderLines.sentToPayment": "<PERSON>t til betaling", "orderDetails.orderLines.addedToPayment": "Lagt til betaling, men ikke sendt", "orderDetails.orderLines.woPopup.noAddress": "<PERSON><PERSON> har ingen adresse", "orderDetails.orderLines.woPopup.totalSum": "Totalt (sum)", "orderDetails.orderLines.woPopup.totalFromTo": "Totalt (fra-til)", "orderDetails.orderLines.woPopup.noTrackings": "Ingen tidsføringer er registrert på denne jobben", "orderDetails.summary.sentToPayment": "Send til betaling", "orders.orderList.confirmed": "Bekreftet", "orders.orderList.createdAt": "Oppret<PERSON><PERSON> dato", "orders.orderList.executionAt": "Utførelsesdato", "orders.orderList.comment": "Kommentar", "orders.orderList.address": "<PERSON><PERSON><PERSON>", "orders.orderList.containsSchedule": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderList.feedbackRating": "Anmeldelse", "orders.orderList.feedbackRating.tooltip": "<PERSON><PERSON><PERSON>", "orders.orderList.feedbackRating.tooltip.noComment": "Ingen kommentar", "orders.orderList.feedbackRating.tooltip.noRating": "Ingen anmeldelse", "orders.orderList.payment": "<PERSON><PERSON>", "orders.orderList.customer": "Kunde", "orders.orderList.serviceRecipient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderList.date": "Da<PERSON>", "orders.orderList.orderID": "Ordre ID", "orders.orderList.title": "<PERSON><PERSON><PERSON>", "orders.orderList.phone": "Telefon", "orders.orderList.source": "Opprettet fra", "orders.orderList.revenue": "Sum", "orders.orderList.sum": "Sum", "orders.orderList.serviceName": "Tjeneste", "orders.orderList.status": "Status", "orders.orderListReports.title": "Ordrerapporter", "orders.searchField": "Søk...", "orders.advancedSearch": "Avansert søk", "orders.status": "Status", "orders.contact": "Kontakt", "orders.filterQuote": "Til<PERSON><PERSON><PERSON><PERSON>", "orders.filterSchedules": "Ordretype", "orders.filterSchedules.all": "Alle", "orders.filterSchedules.repeating": "<PERSON><PERSON><PERSON><PERSON>", "orders.filterSchedules.single": "Enkelt-ordre", "orders.unpaidOrders": "Ubetalte ordre", "orders.unpaidOrdersTooltip": "<PERSON>te vil kun vise ordre som er ubetalt og har ordrestatus '<PERSON><PERSON><PERSON>' el<PERSON> '<PERSON><PERSON> for betaling'", "partner.settings.attributes.newAttributePlaceholder": "<PERSON>ytt attributt", "partner.settings.attributes.newAttributeTypePlaceholder": "Velg type", "partner.tabs.contact": "<PERSON><PERSON><PERSON><PERSON>", "partner.tabs.orders": "Ordre", "partner.tabs.pricing": "Partner-pris", "partner.tabs.settings": "<PERSON>st<PERSON><PERSON>", "partners.contacts.new.companyContactsLabel": "Bedriftskontakter", "partners.contacts.new.createNewCompanyUser": "Op<PERSON>rett ny bruker ved partnerselskapet", "partners.contacts.new.companyContactsPlaceholder": "<PERSON><PERSON><PERSON> etter bedriftskontakter", "partners.paperCompanies.companyId": "Partner ID", "partners.paperCompanies.companyName": "Selskapsnavn", "partners.paperCompanies.contactInfo": "Kontaktinformasjon", "partners.paperCompanies.edit": "<PERSON><PERSON>", "partners.paperCompanies.contactList": "Kontaktliste", "partners.paperCompanies.createNewContact": "Opprett ny kontakt", "partners.paperCompanies.createNewPaperCompany": "<PERSON><PERSON><PERSON><PERSON> ny partner", "partners.paperCompanies.createdAt": "<PERSON><PERSON> opp<PERSON>t", "partners.paperCompanies.editPaperCompany": "<PERSON><PERSON>", "partners.paperCompanies.email": "E-post", "partners.paperCompanies.employee.addHeader": "<PERSON><PERSON><PERSON><PERSON> kontakt", "partners.paperCompanies.employee.header": "Rediger partner", "partners.paperCompanies.employee.attributes": "Partnerattributter", "partners.paperCompanies.employee.create": "<PERSON><PERSON><PERSON><PERSON>", "partners.paperCompanies.employee.edit": "<PERSON><PERSON>", "partners.paperCompanies.employee.email": "E-post", "partners.paperCompanies.employee.firstName": "Fornavn", "partners.paperCompanies.employee.lastName": "Etternavn", "partners.paperCompanies.employee.phone": "Telefon", "partners.paperCompanies.employee.roleDescription": "Rollebeskrivelse", "partners.paperCompanies.organisationNumber": "Organisasjonsnummer", "partners.paperCompanies.phone": "Telefon", "partners.paperCompanies.settings.general.header": "<PERSON><PERSON><PERSON> inn<PERSON>ill<PERSON>", "partners.paperCompanies.settings.accounting.header": "Regnskapsinnstillinger", "partners.paperCompanies.settings.accounting.invoiceConsolidation": "Samlefaktura", "partners.paperCompanies.settings.accounting.dueDateDays": "Fakturaforfall (dager)", "partners.paperCompanies.settings.hidePaymentForCustomer": "<PERSON><PERSON><PERSON><PERSON> priser for kunden", "partners.paperCompanies.settings.tooltip": "Prisinformasjonen vil skjules for kunden hvis partner er satt som betalingsmottaker", "partners.paperCompanies.title": "Partnerselskaper", "partners.settings": "Innstillinger", "partners.settings.attributes": "Attributter", "partners.settings.attributes.addNew": "Legg til nytt attributt", "partners.settings.attributes.attributeName": "Navn", "partners.settings.attributes.attributeType": "Type", "partners.settings.attributes.noAttribute": "Ingen attributter valgt", "partners.settings.save": "Lagre", "partners.settings.close": "Lukk", "partners.settings.title": "Partnerattributter", "placeholders.eMail": "Skriv inn e-post", "placeholders.firstName": "Skriv inn fornavn", "placeholders.lastName": "Skriv inn etternavn", "placeholders.phone": "Skriv inn telefonnummer", "product-list.active": "Aktiv", "product-list.addProduct": "<PERSON>gg til produkt", "product-list.importProducts": "Importer produkter", "product-list.dateAdded": "<PERSON><PERSON> lagt til", "product-list.id": "ID", "product-list.inactive": "Innaktiv", "product-list.name": "Navn", "product-list.price": "<PERSON><PERSON>", "product-list.productType": "Produkttype", "product-list.status": "Status", "products.questionnaire.template.header": "Legg til mal", "products.questionnaire.template.yourTemplates": "<PERSON>e maler", "products.questionnaire.template.subheader": "Velg fra malene du allerede har laget", "products.questionnaire.template.templateName": "Malnavn", "productDetails.questionnaire.template": "Spørreskjema", "products.questionnaire.template.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "products.questionnaire.template.viewQuestion": "<PERSON> spørsmål", "product-list.createProductModal.title": "Opprett et produkt", "product-list.createProductModal.serviceLabel": "Skriv inn navn på produktet", "product-list.createProductModal.placeholder": "Produktnavn", "product-list.createProductModal.dropdownPlaceholder": "Velg produkttype", "product-list.createProductModal.nameDescription": "Gi dette produktet et navn som beskriver den bredt. For eksempel, Hjemmerengjøring i stedet for 1-roms Hjemmerengjøring", "product-list.createProductModal.serviceQuestion": "<PERSON><PERSON>dan ønsker du å bli betalt for dette produktet?", "product-list.createProductModal.nameDescription2": "Når produktet er opprettet kan du opprette flere varianter av det for å tilpasse ytterligere.", "productDetails.productVariants": "Produktvarianter", "productDetails.productVariants.subText": "Ulike varianter av det samme produktet, hver med sin egen pris", "product-list.createProductModal.nameDescriptionTip": "Tips: <PERSON><PERSON> <PERSON>tte produktet et navn som beskriver den bredt. For eksempel, Hjemmerengjøring i stedet for 1-roms Hjemmerengjøring", "productDetails.serviceDetails": "Produktdetaljer", "productDetails.serviceDetails.name": "Navn", "productDetails.serviceDetails.description": "Beskrivelse", "productDetails.serviceDetails.useProductNameAsWorkOrderTitle": "Bruk produktnavn som tittelforslag for jobber", "productDetails.serviceDetails.selectIcon": "<PERSON><PERSON>", "productDetails.serviceDetails.description.hint": "Beskrivelsen vil være synlig for kunden når de bestiller fra et bestillingsskjema eller i et tilsendt tilbud. ", "productDetails.serviceDetails.productType": "Produkttype", "productDetails.serviceDetails.productType.selectProductType": "Velg produkttype", "productDetails.serviceDetails.basePriceIncVat": "Grunnpris inkl. moms", "productDetails.serviceDetails.basePriceExVat": "Grunnpris eksl. moms", "productDetails.serviceDetails.basePrice.hint": "Grunnprisen er hvor mye denne tjenesten koster uten noen prisregler. ", "productDetails.serviceDetails.unitType": "Enhetstype", "productDetails.serviceDetails.unitType.selectUnitType": "Velg enhetstype", "productDetails.serviceDetails.unitType.hint1": "<PERSON><PERSON>s varighet for en kundes avtale bestemmes av tjenestens grunnvarighet + varigheten av eventuelle valgte modifikatorer.", "productDetails.serviceDetails.unitType.hint2": "<PERSON><PERSON>s varighet brukes når tilgjengelighet sjekkes og bestemmer også hvor mye tid som blokkeres i timeplanen.", "productDetails.serviceDetails.taxRate": "MVA-sats", "productDetails.serviceDetails.taxRate.selectTaxRate": "<PERSON><PERSON><PERSON> skat<PERSON>", "productDetails.serviceDetails.linkProduct": "Koble produkt til regnskapssystem", "productDetails.serviceDetails.linkProduct.hint": "Verktøytips for å koble produkt", "productDetails.serviceDetails.subText": "<PERSON>vn, besk<PERSON>lse, pris og enhetstype", "productDetails.productVariants.info": " Legg til produktvariant", "productDetails.productVariants.description": "Produktvarianter lar deg tilby ulike versjoner av et produkt, hver med sin egen pris. <PERSON><PERSON> gjør at du slipper å opprette helt nye produkter for hver variasjon.", "productDetails.productVariants.btn": "Ny gruppe", "productDetails.productVariants.unitPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "productDetails.productVariants.totalPrice": "Totalpris", "productDetails.productVariants.value": "Value", "productDetails.productVariants.priceRuleName": "Navn", "productDetails.productVariants.price": "<PERSON><PERSON> per", "productDetails.productVariants.modal.productName": "Produktvariant navn", "productDetails.productVariants.modal.productName.placeholder": "Skriv inn navet på produktvarianten", "productDetails.productVariants.modal.price": "Price", "productDetails.productVariants.unitPrice.description": "Sett en fast enhetspris for din ordrelinje, slik at prisen per enhet justeres manuelt.", "productDetails.productVariants.totalPriceAdjustment": "Total prisjustering", "productDetails.productVariants.totalPriceAdjustment.description": "Justér den samlede prisen på bestillingen din med en fast prosentandel, enten for å øke eller redusere den totale kostnaden.", "productDetails.customerQuestionnaire": "Spørreskjema til kunde", "productDetails.customerQuestionnaire.subText": "Send egendefinerte spørreskjema til kundene dine på SMS", "productDetails.customerQuestionnaire.title": "Få informasjon fra kunden gjennom et spørreskjema", "productDetails.customerQuestionnaire.description1": "Lag et spørreskjema som sendes til kundene dine via SMS 2 dager før jobb-dato når du selger dette produktet. Legg til spørsmål med enkel- eller flervalg for å samle viktig informasjon, som for eksempel parkeringsmuligheter.", "productDetails.customerQuestionnaire.description2": "<PERSON>nder som bestiller online kan gjøre valg fra disse gruppene for å tilpasse denne tjenesten og få et nøyaktig pristilbud.", "productDetails.customerQuestionnaire.description3": "Lag et spørreskjema som sendes til kundene dine via SMS 2 dager før jobb-dato.", "productDetails.priceAdjustmentRules": "Automatiske prisregler", "productDetails.priceAdjustmentRules.addRule": "Legg til", "productDetails.priceAdjustmentRules.subText": "Tilpass prissettingen når bestemte betingelser er oppfylt", "productDetails.priceAdjustmentRules.info": "Automatiske prisregler gjør det mulig å automatisk justere produktprisen basert på ulike regler", "productDetails.priceAdjustmentRules.timeBasedModifier": "Tidsbasert prisjustering", "productDetails.priceAdjustmentRules.timeBasedModifier.info": "<PERSON>ne regelen justerer prisen basert på dag og tid på døgnet. For eksempel kan du sette høyere priser for arbeid utført utenom vanlig arbeidstid, som overtid.", "productDetails.priceAdjustmentRules.timeBasedModifier.info.short": "<PERSON><PERSON> prisen basert på dag og tid jobben utføres.", "productDetails.priceAdjustmentRules.timeBasedModifier.day": "<PERSON><PERSON>", "productDetails.priceAdjustmentRules.timeBasedModifier.time": "Tid", "productDetails.priceAdjustmentRules.timeBasedModifier.priceAdj": "Prisjustering", "productDetails.priceAdjustmentRules.timeBasedModifier.newPrice": "Ny pris pr enhet", "productDetails.priceAdjustmentRules.timeBasedModifier.weekdays": "<PERSON><PERSON><PERSON>", "productDetails.priceAdjustmentRules.timeBasedModifier.timeRange": "Tidsintervall", "productDetails.priceAdjustmentRules.timeBasedModifier.value": "Verdi", "productDetails.priceAdjustmentRules.timeBasedModifier.adjustedPrice": "<PERSON><PERSON> pris", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.overlapError": "Den nye regelen overlapper med en eksisterende regel.", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.existingRule": "Eksister<PERSON><PERSON> regel", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.info": "<PERSON><PERSON> prisen på denne tjenesten basert på dagen og tidspunktet for avtalen", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.on": "På", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.errorOn": "på", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.weekdays": "Ukes<PERSON><PERSON>", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.prosent": "Prosent", "productDetails.priceAdjustmentRules.timeBasedModifier.modal.timeValidationError": "Starttidspunkt må være mindre enn sluttidspunkt", "productDetails.priceAdjustmentRules.volumeDiscount": "<PERSON><PERSON> en<PERSON><PERSON><PERSON> etter k<PERSON>t", "productDetails.priceAdjustmentRules.modal.title": "Legg til ny enhetspris", "productDetails.priceAdjustmentRules.volumeDiscount.info": "Lag regler som justerer enhetsprisen innenfor angitte intervaller. Du kan sette flere intervaller for å variere prisen basert på ulike kvantiteter.", "productDetails.priceAdjustmentRules.volumeDiscount.short": "Lag regler som justerer enhetsprisen innenfor angitte intervaller", "productDetails.priceAdjustmentRules.between": "<PERSON><PERSON><PERSON>", "productDetails.priceAdjustmentRules.volumeDiscount.newPrice": "Pris per enhet", "productDetails.priceAdjustmentRules.quantityRange": "Intervall", "productDetails.priceAdjustmentRules.newPriceInclVat": "Ny pris per enhet inkl. moms", "productDetails.priceAdjustmentRules.newPriceExVat": "<PERSON>y pris per enhet eksl. moms", "productDetails.priceAdjustmentRules.newFixedPriceExVat": "Fast pris eksl. moms", "productDetails.priceAdjustmentRules.newFixedPriceInclVat": "Fast pris inkl. moms", "productDetails.priceAdjustmentRules.tieredQuantityPricing": "Fast pris basert på kvantitet", "productDetails.priceAdjustmentRules.tieredQuantityPricing.add": "Legg til trinn", "productDetails.priceAdjustmentRules.tieredQuantityPricing.info": "Sett faste priser for bestemte kvantitetsintervaller. <PERSON><PERSON> lar deg definere en fast pris innenfor spesifikke kvantumsintervaller. For eksempel, et vaskebyrå kan sette prisen til 1000 \nNOK for områder mellom 0-50 kvm, 2000 NOK for 51-100 kvm, osv.", "productDetails.priceAdjustmentRules.tieredQuantityPricing.totalPriceExVat": "Totalpris eksl. moms", "productDetails.priceAdjustmentRules.tieredQuantityPricing.totalPriceIncVat": "Totalpris inkl. moms", "productDetails.priceAdjustmentRules.tieredQuantityPricing.info.short": "<PERSON><PERSON> re<PERSON>en setter en fast pris for spesifiserte intervaller.", "productDetails.priceAdjustmentRules.tieredQuantityPricing.value": "Verdi", "productDetails.priceAdjustmentRules.tieredQuantityPricing.modal.rangeComparisonError": "Området 'fra' må være mindre enn området 'til'.", "productDetails.priceAdjustmentRules.tieredQuantityPricing.modal.existingRuleRange": "Eksisterende regelområde", "productDetails.priceAdjustmentRules.tieredQuantityPricing.modal.rangeOverlapError": "Den nye regelen overlapper med en eksisterende regel.", "productDetails.priceAdjustmentRules.tieredQuantityPricing.modal.priceRuleName": "Navn", "productDetails.priceAdjustmentRules.tieredQuantityPricing.modal.enterPriceRuleName": "Skriv inn navn på prisregel", "productDetails.crewChecklist": "Sjekkliste", "productDetails.crewChecklist.subText": "<PERSON><PERSON><PERSON><PERSON> ansatte med å huske alle oppgaver som skal gjøres", "productDetails.crewChecklist.addChecklist": "Legg til sjekkliste", "productDetails.crewChecklist.taskGroup.tooltip": "Skriv inn et navn for denne gruppen av alternativer. Dette navnet vises øverst i gruppen når kunder bestiller online.", "productDetails.crewChecklist.stage.tooltip": "<PERSON>nder og teammedlemmer vil kunne velge fra denne gruppen for å tilpasse tjenesten som blir planlagt. Hvert modifiseringsalternativ kan ha sin egen pris og varighet, som vil bli lagt til tjenesten hvis valgt.", "productDetails.upsell": "Mersalg", "productDetails.upsell.subText": "Gi kundene muligheten til å velge tilleggsprodukter når de bestiller via bookingskjemaet eller mottar et tilbud", "productDetails.upsell.title": "Øk salget med mersalgsprodukter", "productDetails.upsell.description": "Gi kundene muligheten til å velge tilleggsprodukter når de bestiller via bookingskjemaet eller mottar et tilbud. På denne måten kan kunder se å kjøpe andre ting de kanskje trenger.", "productDetails.upsell.addProducts": "<PERSON><PERSON> til produkter", "productDetails.upsell.selectedProducts": "<PERSON><PERSON><PERSON> produkter", "productDetails.upsell.noSelectedProducts": "Du har ikke valgt noen mersalgprodukter", "productDetails.upsell.selectedUpsell": "Valgte oppsalgprodukter", "productDetails.upsell.connect": "<PERSON><PERSON> andre produkter til denne tjenesten og tilby dem sammen på bestillingssiden.", "productDetails.upsell.selectedProducts.tooltip": "Vis og administrer produktene du har valgt å tilby sammen med din hovedtjeneste. Dette er hva kundene dine vil se som tilleggsmuligheter ved bestilling.", "productDetails.upsell.availableProducts": "Tilgjengelige produkter", "productDetails.upsell.availableProducts.tooltip": "<PERSON><PERSON> gjennom den fullstendige listen over produkter du kan koble til tjenesten din. For å legge til noen til dine oppsalgsalternativer, velg dem fra denne listen.", "productDetails.upsell.preview": "Forhåndsvisning", "productDetails.upsell.wouldYouLike": "Vil du og<PERSON>å kjøpe?", "productDetails.upsell.continue": "Fortsett", "productDetails.upsell.addService": "Legg til tjeneste", "productDetails.upsell.manage": "Administrer oppsalgprodukter", "productDetails.upsell.searchProducts": "<PERSON><PERSON><PERSON> produkter", "productDetails.upsell.noAvailable": "Ingen tilgjengelige produkter", "products.checklists.header": "Sjekkliste", "products.checklists.enterTaskGroupName": "Skriv inn kategorinavn", "products.checklists.addGroupModal.addButton": "Legg til gruppe", "products.checklists.addGroupModal.addButton.task": "Nytt punkt", "products.checklists.addGroupModal.AddTemplate": "Velg sjekkliste fra mal", "products.checklists.templates.title": "Sjekkliste maler for mannskap", "products.checklists.templates.choose": "Velg sjekkliste mal du vil inkludere i denne jobben", "products.checklists.templates.none": "Du har ingen sjekklistemaler", "products.checklists.templates.createOne": "<PERSON><PERSON><PERSON> her for å opprette en mal", "products.checklists.templates.addChecklistButton": "Legg til sjekkliste", "products.checklists.templates.addCustomChecklistButton": "Op<PERSON><PERSON>t egen sjekkliste", "products.checklists.templates.addChosenTemplates": "<PERSON>gg til valgte maler", "products.checklists.addChecklistBtn": "Legg til sjekkliste", "products.checklists.addGroupModal.addChecklistGroupFor": "Legg til sjekklistegruppe for", "products.checklists.addGroupModal.close": "Lukk", "products.checklists.addGroupModal.groupName": "Gruppenavn", "products.checklists.addGroupModal.save": "Lagre", "products.checklists.addGroupModal.taskGroupName": "Navn på sjekklistegruppe", "products.checklists.addStageBeforeCreatingTasks": "Du må legge til et steg før du lager sjekklister", "products.checklists.addTaskModal.addTaskFor": "Legg til oppgave for", "products.checklists.addTaskModal.close": "Lukk", "products.checklists.addTaskModal.save": "Lagre", "products.checklists.addTaskModal.taskName": "Oppgavenavn", "products.checklists.categories": "Sjekklistekategorier", "products.checklists.categories.noTaskGroups": "Ingen sjekklister. Legg til en ny sjekkliste eller importer fra malene dine", "products.checklists.categoriesDescipription": "Sjekklistekategorier lar deg organisere sjekklistene dine i grupper for mannskapet ditt", "products.checklists.checklist": "Sjekkliste for", "products.checklists.checklistCrewApp": "Sjekkliste", "products.checklists.checklistDescription.title": "<PERSON><PERSON><PERSON><PERSON> ansatte med å huske alle oppgaver som skal gjøres", "products.checklists.checklistDescription": "En sjekkliste for dine ansatte bidrar til å sikre at ingen oppgaver blir oversett og at arbeidet blir utført korrekt. For å kunne markere en jobb som fullført, må alle elementer på sjekklisten enten være gjennomført eller det må rapporteres om eventuelle avvik.", "products.checklists.contract": "Skrivet kontrakt med kunde", "products.checklists.create": "<PERSON><PERSON><PERSON><PERSON>", "products.checklists.createChecklistforStage": "<PERSON>gge til en egen sjekk liste kun for denne aktiviteten.", "products.checklists.createCrewChecklist": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>k<PERSON>", "products.checklists.editCrewChecklist": "Rediger sjekkliste", "products.checklists.createNewChecklist": "<PERSON><PERSON><PERSON><PERSON> sjekk liste for", "products.checklists.crewChecklist": "Sjekkliste i ansatt-app", "products.checklists.defaultName": "Sjekkliste", "products.checklists.templateName": "Malnavn", "products.checklists.items": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "products.checklists.categoryName": "Kategorinavn", "products.checklists.newListItem": "Nytt punkt", "products.checklists.noSpacing": "Oppgavenavnet kan ikke være tomt eller kun bestå av mellomrom", "products.checklists.noStageSelected": "Ingen steg valgt", "products.checklists.noChecklist": "<PERSON><PERSON> steget har ingen sjekk<PERSON>er", "products.checklists.preview": "Forhåndsvisning", "products.checklists.save": "Lagre", "products.checklists.selectStage": "<PERSON><PERSON>g steg", "products.checklists.sluk": "Tømt sluk", "products.checklists.soppel": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON>", "products.checklists.stage.group.addTask": "Legg til oppgave", "products.checklists.stage.stage": "Steg:", "products.checklists.stage.taskGroup": "Sjekklistegruppe", "products.checklists.stageTasks": "oppgaver", "products.checklists.taskNameValidation": "Oppgave navn kan ikke være tomt", "products.checklists.taskPlaceholder": "Sjekklistepunkt", "products.checklists.taskRequired": "Du trenger minst én oppgave.", "products.checklists.categorized": "Kategorisert", "products.checklists.newCategory": "<PERSON><PERSON> ka<PERSON>gor<PERSON>", "products.editProduct.checklists": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "products.editProduct.customerQuestionnaire": "Spørreskjema", "products.editProduct.generalInformation": "Generell informasjon", "products.editProduct.importantInformation": "Viktig informasjon", "products.editProduct.inventory": "Beholdning", "products.editProduct.recurringOptions": "Gjentakende alternativer", "products.editProduct.quantityCalculations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "products.editProduct.quantityCalculations.noSource": "Ingen kilde", "productDetails.advancedSettings": "Avanserte innstillinger", "productDetails.advancedSettings.subheading": "<PERSON><PERSON> a<PERSON>te innstillinger", "productDetails.advancedSettings.subText": "", "productDetails.advancedSettings.addStage": "<PERSON><PERSON> til fase", "productDetails.advancedSettings.billable": "Fak<PERSON><PERSON><PERSON><PERSON>", "productDetails.advancedSettings.billableTooltip": "<PERSON><PERSON> fasen er fakturérbar, vil den bli inkludert i den automatiske prisberegningen basert på logget varighet av bestillingen", "productDetails.advancedSettings.billableTransport": "Fakturérbar transport", "productDetails.advancedSettings.billableTransportTooltip": "H<PERSON> fasen er satt til fakturérbar transport, vil transportfasen bli inkludert i den automatiske prisberegningen basert på logget varighet av bestillingen", "productDetails.advancedSettings.duplicatable": "Kan kopieres", "productDetails.advancedSettings.duplicatableTooltip": "<PERSON><PERSON> fasen er satt til å kunne kopiere, vil fasen være tilgjengelig for kopiering ved opprettelse av bestilling", "productDetails.advancedSettings.useInCalculation": "Bruk i beregning", "productDetails.advancedSettings.useInCalculationTooltip": "Bestem om adressen på fasen skal brukes i den automatiske beregningen av mengde/varighet for bestillinger", "productDetails.advancedSettings.includeTransport": "Inkluder transport", "productDetails.advancedSettings.includeTransportTooltip": "<PERSON><PERSON> fasen er satt til å inkludere transport, vil fasen implementere en sporbar transportfase før fasen starter", "productDetails.advancedSettings.setAddress": "<PERSON>t adresse", "productDetails.advancedSettings.setAddressTooltip": "<PERSON><PERSON> fasen er satt til å inkludere en adresse, vil fasen kreve adresseinput ved opprettelse av bestilling", "productDetails.advancedSettings.settings": "Innstillinger", "productDetails.advancedSettings.settingsTooltip": "<PERSON>nder og teammedlemmer vil kunne velge fra denne gruppen for å tilpasse tjenesten som planlegges. Hvert modifiseringsalternativ kan ha sin egen pris og varighet, som vil bli lagt til tjenesten hvis valgt.", "productDetails.advancedSettings.stageName": "Fasenavn", "productDetails.advancedSettings.stageNameTooltip": "Skriv inn et navn for denne gruppen av alternativer. Dette navnet vises øverst i gruppen når kunder bestiller online.", "productDetails.advancedSettings.infoTitle": "Automatisk opprettelse av jobb", "productDetails.advancedSettings.infoDescription": "Bruk disse innstillingene for å automatisk opprette en jobb når en ordrelinje med dette produktet legges til ved en ordre. Bestem tittelen på jobben og standard adresseoppsett.", "productDetails.advancedSettings.workOrderEnable": "Opprett jobb automatisk", "productDetails.advancedSettings.workOrderTitle": "<PERSON><PERSON><PERSON> p<PERSON>b", "productDetails.advancedSettings.addressesTitle": "<PERSON><PERSON><PERSON> på <PERSON>e", "productDetails.recurringOptions": "<PERSON>jentaken<PERSON> bestillinger", "productDetails.recurringOptions.subText": "<PERSON><PERSON> andre produkter til denne tjenesten og tilby dem sammen på bestillingssiden", "products.editProduct.pricing": "<PERSON><PERSON><PERSON><PERSON>", "products.editProduct.stages": "Steg", "products.editProduct.upsellProducts": "Mersalg", "products.generalInfo.active": "Aktiv", "products.generalInfo.activeSwitchContainer": "aktiv-switch-container", "products.generalInfo.addStageBeforeActivating": "Vennligst legg til et steg før du aktiverer produktet", "products.generalInfo.description": "Beskrivelse", "products.generalInfo.enterPrice": "<PERSON><PERSON> pris", "products.generalInfo.enterProductDescription": "Gi en produktbeskrivelse", "products.generalInfo.enterProductTitle": "<PERSON><PERSON> produkt<PERSON><PERSON>l", "products.generalInfo.inactiveSwitchContainer": "inaktiv-switch-container", "products.generalInfo.price": "<PERSON><PERSON>", "products.generalInfo.productDescription": "Produktbeskrivelse", "products.generalInfo.productIcon": "Produktikon", "products.generalInfo.productTitle": "Produkttittel", "products.generalInfo.productType": "Produkttype", "products.generalInfo.saveProduct": "Lagre produkt", "products.generalInfo.savingProduct": "Lagrer produkt...", "products.generalInfo.connectToAccounting": "Koble produktet til et eksisterende produkt i regnskapssystemet ditt", "products.generalInfo.searchAccounting": "Sø<PERSON> etter produkt i regnskapssystemet", "products.generalInfo.selectAUnit": "Velg en enhet", "products.generalInfo.selectAnOption": "Velg et alternativ", "products.generalInfo.selectProductType": "Velg produkttype", "products.generalInfo.selectVatType": "Velg MVA-type", "products.generalInfo.unitNotAvailable": "ikke <PERSON> for", "products.generalInfo.unitType": "Enhetstype", "products.generalInfo.vatType": "MVA-type", "products.icons.title": "Venligst velg et ikon", "productDetails.importantInformation": "Viktig informasjon", "productDetails.importantInformation.subText": "Vis attributer ved denne tjenesten eller bedriften din når kunder bestiller online", "productDetails.importantInformation.addImportantInformation": "Legg til viktig informasjon som kunden vil se når de bestiller dette produktet", "productDetails.importantInformation.addModal.close": "Lukk", "productDetails.importantInformation.addModal.description": "Beskrivelse", "productDetails.importantInformation.addModal.descriptionPlaceholder": "Beskrivelse...", "productDetails.importantInformation.addModal.modalTitle": "Legg til ny seksjon", "productDetails.importantInformation.addModal.required": "<PERSON><PERSON><PERSON> kundegodk<PERSON>nning ved lesing", "productDetails.importantInformation.addModal.save": "Lagre", "productDetails.importantInformation.addModal.title": "<PERSON><PERSON><PERSON>", "productDetails.importantInformation.addNewInformation": "Legg til ny informasjon", "productDetails.importantInformation.customerName": "Kundenavn", "productDetails.importantInformation.importantInformation": "Viktig informasjon", "productDetails.importantInformation.noInformationAvailable": "Ingen produktinformasjon tilgjengelig", "productDetails.importantInformation.orderEstimate": "Bestillingsestimat", "productDetails.importantInformation.preview": "Forhåndsvisning", "productDetails.importantInformation.title": "Viktig informasjon", "productDetails.importantInformation.highlight": "Fr<PERSON>hev funksjonene til denne tjenesten eller din bedrift, svar på vanlige spørsmål kunder kan ha, og vis anmeldelser fra andre kunder.", "productDetails.importantInformation.addInfo.title": " Legg til informasjon vedrørende dette produktet", "productDetails.importantInformation.addInfo.description": "Viktig informasjon inneholder viktige detaljer som vises i bestillingskjema og tilbud. Du kan opprette dine egne informasjonskategorier og tilpassede tekster. Kundene signerer for den viktige informasjonen, noe som bidrar til å redusere klager og misforståelser. ", "productDetails.importantInformation.modal.header.description": "<PERSON><PERSON> prisen på denne tjenesten basert på dagen og tidspunktet for avtalen", "productDetails.importantInformation.modal.sectionHeader": "Seksjonstittel", "productDetails.importantInformation.modal.description": "Beskrivelse", "productDetails.importantInformation.modal.required": "Beskrivelse", "productDetails.importantInformation.noStages": "Ingen steg tilg<PERSON>g", "productDetails.importantInformation.addStage": "Ingen produktsteg er lagt til. Vennligst legg til et steg i avanserte innstillinger for å legge til sjekkliste.", "productDetails.advancedSettings.info": "Avanserte innstillinger lar deg tilpasse tjenesten ytterligere. Du kan legge til krav om flere adresser når tjenesten utføres, kopiering av adresser og automasjon i kalkulering av tid. Ved spørsmål kontakt support.", "products.importantInformation.addImportantInformation": "Legg til viktig informasjon som kunden vil se når bestiller dette produktet.", "products.importantInformation.addModal.close": "Lukk", "products.importantInformation.addModal.description": "Beskrivelse", "products.importantInformation.addModal.descriptionPlaceholder": "Beskrivelse...", "products.importantInformation.addModal.modalTitle": "Legg til ny seksjon", "products.importantInformation.addModal.required": "Krev aksept av kunden", "products.importantInformation.addModal.save": "Lagre", "products.importantInformation.addModal.title": "<PERSON><PERSON><PERSON>", "products.importantInformation.addNewInformation": "Legg til ny informasjon", "products.importantInformation.customerName": "Kundenavn", "products.importantInformation.importantInformation": "Viktig informasjon", "products.importantInformation.noInformationAvailable": "Ingen produktinformasjon tilgjengelig", "products.importantInformation.orderEstimate": "Ordreestimat", "products.importantInformation.preview": "Forhåndsvisning", "products.inventory.enterSku": "SKU", "products.inventory.enterStockQuantity": "<PERSON><PERSON> nåværende lagerbeholdning", "products.inventory.enterWeight": "<PERSON><PERSON> vekt", "products.inventory.sku": "SKU", "products.inventory.stockQuantity": "Lagerbeholdning", "products.inventory.weight": "Vekt", "products.newProduct.checklists": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "products.newProduct.customerQuestionnaire": "Spørreskjema", "products.newProduct.generalInformation": "Generell informasjon", "products.newProduct.importantInformation": "Viktig informasjon", "products.newProduct.inventory": "Beholdning", "products.newProduct.pageTitle": "Nytt produkt", "products.newProduct.pricing": "<PERSON><PERSON><PERSON><PERSON>", "products.newProduct.stages": "Steg", "products.newProduct.upsellProducts": "Mersalg", "products.overview.title": "<PERSON>du<PERSON><PERSON>", "products.pricing.addNewPriceGroup": "Legg til ny prisgruppe", "products.pricing.addPricingModal.automaticTriggers": "Automatisk aktiverte", "products.pricing.addPricingModal.closeButtonLabel": "Lukk", "products.pricing.addPricingModal.groupNameLabel": "Navn på prisgruppe", "products.pricing.addPricingModal.groupNamePlaceholder": "Navn på prisgruppe", "products.pricing.addPricingModal.manualTriggers": "Manuelt aktiverte", "products.pricing.addPricingModal.modalTitle": "Legg til ny prisgruppe", "products.pricing.addPricingModal.priceNullUnitPrice": "En pris på 0 er ikke tillatt for denne prisutløseren", "products.pricing.addPricingModal.saveButtonLabel": "Lagre gruppe", "products.pricing.addPricingModal.triggerLabel": "Velg trigger", "products.pricing.addPricingModal.triggerPlaceholder": "Velg...", "products.pricing.addPricingModal.triggerSelectId": "trigger_type", "products.pricing.editPricingModal.closeButtonLabel": "Lukk", "products.pricing.editPricingModal.deleteGroup": "Slett gruppe", "products.pricing.editPricingModal.groupNameLabel": "Navn på prisgruppe", "products.pricing.editPricingModal.groupNamePlaceholder": "Navn på prisgruppe", "products.pricing.editPricingModal.modalTitle": "Rediger prisgruppe", "products.pricing.editPricingModal.saveButtonLabel": "Lagre gruppe", "products.pricing.editPricingModal.sure": "Sikker?", "products.pricing.group.addNewPriceRule": "Legg til ny prisregel", "products.pricing.group.edit": "rediger", "products.pricing.group.rule.calculation": "Beregning", "products.pricing.group.rule.endTime": "S<PERSON><PERSON><PERSON>", "products.pricing.group.rule.errorOverlappingQuantity": "Overlapper", "products.pricing.group.rule.errorOverlappingTime": "Overlapper", "products.pricing.group.rule.newPriceExVat": "<PERSON>y pris eksl. moms", "products.pricing.group.rule.newPriceIncVat": "<PERSON>y pris inkl. moms", "products.pricing.group.rule.priceRuleName": "Navn på prisregel", "products.pricing.group.rule.qtyFrom": "Antall fra", "products.pricing.group.rule.qtyTo": "An<PERSON><PERSON> til", "products.pricing.group.rule.startTime": "Starttid", "products.pricing.group.rule.totalPrice": "Totalpris", "products.pricing.group.rule.value": "Verdi", "products.pricing.group.rule.weekdays": "<PERSON><PERSON><PERSON>", "products.pricing.header": "<PERSON><PERSON> pris", "products.pricing.includeEmbed": "<PERSON><PERSON><PERSON><PERSON> i embed", "products.pricing.includeEmbedTooltip": "Velg dette alternativet hvis du ønsker å inkludere prisregelen i embed-koden", "products.pricing.price": "<PERSON><PERSON>", "products.pricing.individualTimeTracking": "Individuell tidsregistrering", "products.pricing.individualTimeTrackingTooltip": "Aktiver for å akkumulere tid registrert ved en jobb. <PERSON><PERSON> betyr at to ansatte som jobber samtidig vil doble prisen kunden betaler for jobben.", "products.pricing.pricingGroup": "prisgruppe", "products.pricing.testPricing": "Test prissetting", "products.pricing.testPricingModal.calculatedPriceLabel": "Be<PERSON>gnet produktpris", "products.pricing.testPricingModal.closeButtonLabel": "Lukk", "products.pricing.testPricingModal.dateLabel": "Da<PERSON>", "products.pricing.testPricingModal.endTimeLabel": "S<PERSON><PERSON><PERSON>", "products.pricing.testPricingModal.productLabel": "Produkt", "products.pricing.testPricingModal.productNameLabel": "Produkt", "products.pricing.testPricingModal.quantityInfoLabel": "<PERSON><PERSON><PERSON>", "products.pricing.testPricingModal.quantityLabel": "<PERSON><PERSON><PERSON>", "products.pricing.testPricingModal.quantityPlaceholder": "<PERSON><PERSON> antallet du vil teste med", "products.pricing.testPricingModal.simulateOrderButtonLabel": "Simuler ordre", "products.pricing.testPricingModal.startTimeLabel": "Starttid", "products.pricing.testPricingModal.title": "Test prisregler", "products.pricing.testPricingModal.totalPriceLabel": "Total pris", "products.pricing.testPricingModal.totalPriceAdjustment": "Total prisjustering", "products.pricing.testPricingModal.unitPriceLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "products.product-details.pricing.price-rule-groups.price-rules.calculationTypes.amount": "<PERSON><PERSON><PERSON>", "products.product-details.pricing.price-rule-groups.price-rules.calculationTypes.percentage": "Prosent", "products.product-details.pricing.price-rule-groups.price-rules.manual-default-name": "<PERSON><PERSON>", "products.quantityCalculations.addNewCalculation": "<PERSON>gg til kalkulering", "products.quantityCalculations.editCalculation": "<PERSON><PERSON>", "products.quantityCalculations.testCalculation": "Test kalkulering", "products.quantityCalculations.testCalculation.run": "Ka<PERSON><PERSON>r", "products.quantityCalculations.testCalculation.result": "Resultat", "products.quantityCalculations.testCalculation.crewSize": "Antall crew", "products.quantityCalculations.calculationsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "products.quantityCalculations.valueSource": "Datakilde", "products.quantityCalculations.operation": "Operasjon", "products.quantityCalculations.factor": "<PERSON><PERSON><PERSON>", "products.quantityCalculations.equationOperation": "Ligningsoperasjon", "products.quantityCalculations.enableCalculationLabel": "Aktiver automatisk kalkulering for dette produktet", "products.quantityCalculations.transportTimeLabel": "Legg til estimert transporttid til utregningsresultatet", "products.questionnaire.addChoice": "<PERSON>gg til valg", "products.questionnaire.addQuestionnaire": "Legg til spørsmål", "products.questionnaire.addQuestionnaireModal.addCommentBtn": "Legg til kommentarfelt", "products.questionnaire.addQuestionnaireModal.close": "Lukk", "products.questionnaire.addQuestionnaireModal.commentDescription": "La kundene legge til en kommentar når de velger dette svaral<PERSON>t.", "products.questionnaire.addQuestionnaireModal.commentPlaceholder": "F.<PERSON><PERSON> «Legg inn antall esker»", "products.questionnaire.addQuestionnaireModal.continue": "Fortsett", "products.questionnaire.addQuestionnaireModal.description": "Beskrivelse", "products.questionnaire.addQuestionnaireModal.descriptionPlaceholder": "Legg til en beskrivelse her", "products.questionnaire.addQuestionnaireModal.ghostTextPlaceholder": "", "products.questionnaire.addQuestionnaireModal.header": "Lag nytt spørsmål", "products.questionnaire.addQuestionnaireModal.multiSelect": "<PERSON><PERSON><PERSON><PERSON>", "products.questionnaire.addQuestionnaireModal.newOption": "<PERSON>gg til valg", "products.questionnaire.addQuestionnaireModal.optionNameRequired": "Minst 1 alternativ er påkrevd", "products.questionnaire.addQuestionnaireModal.optionPlaceholder": "Alternativ {{value}}", "products.questionnaire.addQuestionnaireModal.options": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "products.questionnaire.addQuestionnaireModal.preview": "Forhåndsvisning", "products.questionnaire.addQuestionnaireModal.question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "products.questionnaire.addQuestionnaireModal.question.header": "Spørreskjema", "products.questionnaire.addQuestionnaireModal.questionPlaceholder": "Skriv inn spørsmålet ditt her", "products.questionnaire.addQuestionnaireModal.questionType": "Spørsmålstype", "products.questionnaire.addQuestionnaireModal.required": "Påkrevd", "products.questionnaire.addQuestionnaireModal.save": "Lagre", "products.questionnaire.addQuestionnaireModal.selectionType": "Valgt<PERSON>", "products.questionnaire.addQuestionnaireModal.singleSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "products.questionnaire.addQuestionnaireModal.specificationTextRequired": "<PERSON>te feltet er påkrevd", "products.questionnaire.addQuestionnaireModal.tooltipMultipleSelect": "La kundene velge flere av svaralternativene. Kan brukes som en sjekkliste mot kunden.", "products.questionnaire.addQuestionnaireModal.tooltipQuestion": "<PERSON><PERSON> et navn for dette spørsmålet. Dette navnet vises øverst i gruppen når kunder bestiller online.", "products.questionnaire.addQuestionnaireModal.tooltipRequired": "<PERSON><PERSON><PERSON><PERSON> det påkrevd for kunden å svare på spørsmålet.", "products.questionnaire.addQuestionnaireModal.tooltipSingleSelect": "La kundene velge kun ett av svaralternativene.", "products.questionnaire.editQuestionnaire.title": "<PERSON><PERSON>", "products.questionnaire.addShortAnswer": "<PERSON>gg til kort svar", "products.questionnaire.choiceModal.addRequest": "Legg til en forespørsel om ytterligere spesifikasjon", "products.questionnaire.choiceModal.addShortItem": "<PERSON>gg til kort svar", "products.questionnaire.choiceModal.choiceName": "Navn", "products.questionnaire.choiceModal.close": "Lukk", "products.questionnaire.choiceModal.modalTitle": "Legg til element", "products.questionnaire.choiceModal.remove": "<PERSON><PERSON><PERSON>", "products.questionnaire.choiceModal.save": "Lagre", "products.questionnaire.continueBtn": "Fortsett", "products.questionnaire.customerName": "Kundenavn", "products.questionnaire.customerQuestionnaire": "Opprett et spørreskjema", "products.questionnaire.customerQuestionnaireLabel": "Legg til tilpassede skjemafelter for å samle inn ekstra informasjon", "products.questionnaire.doorman": "Portvakt", "products.questionnaire.editBtn": "<PERSON><PERSON>", "products.questionnaire.emptyQuestionnaireDescripption1": "Spørreskjema lar deg samle inn informasjon fra kundene før en jobb skal gjennomføres. Du kan lage dine egne spørsm<PERSON>l her.", "products.questionnaire.emptyQuestionnaireDescripption2": "<PERSON>nder som bestiller online, kan velge fra disse gruppene for å tilpasse denne tjenesten og få en nøyaktig pris.", "products.questionnaire.hiddenKey": "Sk<PERSON><PERSON> n<PERSON>k<PERSON>", "products.questionnaire.howDoWeGetIn": "Hvordan kommer vi inn?", "products.questionnaire.intakeDescription": "Spørreskjema lar deg samle ekstra informasjon fra kundene dine under bestillingsprosessen ved å bruke egendefinerte felt.", "products.questionnaire.intakeQuestions": "Spørreskjema", "products.questionnaire.newQuestionnaireBtn": "<PERSON>y spø<PERSON>und<PERSON>økel<PERSON>", "products.questionnaire.noQuestionnaireAvailable": "Ingen spørreskjema tilgjengelig", "products.questionnaire.orderConfirmation": "Ordrebekreftelse", "products.questionnaire.preview": "Forhåndsvisning", "products.questionnaire.question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "products.questionnaire.someoneIsHome": "<PERSON><PERSON> er hjemme", "products.stage.addNewStage": "Legg til nytt steg", "products.stage.addOrEditStages": "Legg til eller rediger steg for dette produktet", "products.stage.addStageModal.closeButtonLabel": "Lukk", "products.stage.addStageModal.customStageNameLabel": "Navn på steg", "products.stage.addStageModal.modalTitle": "Legg til nytt steg", "products.stage.addStageModal.saveStageButtonLabel": "Lagre steg", "products.stage.addStageModal.stageTypeLabel": "Type steg", "products.stage.addStageModal.stagesMarkedNotice": "Steg merket med (*) vil legge til et ekstra adressefelt til ordren", "products.stage.advancedView": "Avansert visning", "products.stage.autoStart": "Auto-start", "products.stage.autoStartTooltip": "<PERSON>vis steget settes til auto-start vil det startes automatisk når forrige steg fullføres (el<PERSON>, hvis det er det første steget i ordren, når jobben startes)", "products.stage.billable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "products.stage.billableTooltip": "<PERSON><PERSON> steget er fakturerbart vil det bli inkludert i den automatiske priskalkuleringen som baserer seg på ordrens loggførte utførelsestid", "products.stage.billableTransport": "Fakturér transport", "products.stage.billableTransportTooltip": "Hvis transport er satt til fakturerbar vil transportetappen bli inkludert i den automatiske priskalkuleringen som baserer seg på ordrens loggførte utførelsestid", "products.stage.customerName": "Kundenavn", "products.stage.duplicatable": "<PERSON><PERSON><PERSON><PERSON>", "products.stage.duplicatableTooltip": "<PERSON><PERSON> steget er satt til å være Kopierbar vil det være mulig å legge til flere instanser av steget i samme ordre", "products.stage.useInCalculation": "Bruk i kalkulering", "products.stage.useInCalculationTooltip": "Bestem om adressen på steget skal benyttes som grunnlag for automatisk kalkulering av kvantitet/varighet for ordre", "products.stage.finished": "<PERSON><PERSON><PERSON><PERSON>", "products.stage.includeTransport": "Inkluder transport", "products.stage.includeTransportTooltip": "<PERSON><PERSON> steget er satt til å inkludere transport vil det bli lagt til en transportfase før steget startes", "products.stage.notifyCustomer": "Kundemelding", "products.stage.notifyCustomerTooltip": "<PERSON><PERSON> steget er satt til å sende kundemelding vil kunden motta en melding om at jobben er påbegynt i det øyeblikket steget starter (dette vil ikke overstyre varselinnstillingene satt for selskapet)", "products.stage.pleaseAddAStageForPreview": "Vennligst legg til et steg for forhåndsvisning", "products.stage.preview.orderID": "Ordre #57", "products.stage.preview.title": "Forhåndsvisning", "products.stage.preview.yourOrderStatus": "<PERSON>", "products.stage.setAddress": "<PERSON>t adresse", "products.stage.setAddressTooltip": "<PERSON><PERSON> steget er satt til å sette adresse vil steget kreve at en adresse blir satt under ordreopprettelse", "products.stage.stageName": "Navn på steg", "products.stage.stageTutorial": "Veileding", "products.stage.stageType": "Type steg", "products.stage.yourOrderStatus": "<PERSON>", "products.upsellProducts.add": "Legg til", "products.upsellProducts.availableUpsellProducts": "Tilgjengelige mersalgsprodukter", "products.upsellProducts.customerName": "Kundenavn", "products.upsellProducts.noUpsellProducts": "Ingen mersalgsprodukter", "products.upsellProducts.noUpsellProductsAvailable": "Ingen mersalgsprodukter tilgjengelig", "products.upsellProducts.orderEstimate": "Bestillingsestimat", "products.upsellProducts.preview": "Forhåndsvisning", "products.upsellProducts.savedUpsellProducts": "Lag<PERSON>e mersa<PERSON>gsprodukter", "products.recurringOptions": "Gjentakende alternativer", "products.recurringOptions.description": "Definer gjentakende frekvenser nedenfor hvis du ønsker å tillate kunder å bestille denne tjenesten som en gjentakende avtale.", "products.recurringOptions.addRecurringOption": "Legg til gjentakende alternativ", "products.recurringOptions.createRecurringOption": "Opprett gjentakende alternativ", "products.recurringOptions.frequency": "<PERSON><PERSON><PERSON><PERSON>", "products.recurringOptions.discount": "<PERSON><PERSON><PERSON>", "products.recurringOptions.name": "Navn", "products.recurringOptions.every": "Hver", "products.recurringOptions.week": "uke(r)", "products.recurringOptions.nthWeekItem.1": "1.", "products.recurringOptions.nthWeekItem.2": "2.", "products.recurringOptions.nthWeekItem.3": "3.", "products.recurringOptions.nthWeekItem.4": "4.", "registerUser.confirmPassword": "Verifiser passord", "registerUser.confirmPasswordLabel": "Skriv inn ditt passord igjen", "registerUser.description": "Bekreft e-post og passord for å gå tilgang til Between.", "registerUser.email": "E-post", "registerUser.emailLabel": "ola_<PERSON><PERSON><EMAIL>", "registerUser.loginLinkLabel": "Between Core", "registerUser.password": "Passord", "registerUser.passwordLabel": "Skriv inn ditt passord ", "registerUser.registered": "Du er nå registrert. Vennligst logg inn for å fortsette.", "registerUser.submitButtonLabel": "Bekreft", "registerUser.title": "Registrer bruker", "registerUser.error.title": "Ugyldig invitasjon", "registerUser.error.description": "Invitasjonen du har benyttet er ugyldig. Vennligst verifiser at du har benyttet den siste invitasjonen du mottok, ettersom tidligere invitasjoner blir ugyldige når du mottar en ny invitasjon.", "reports.capturedPayments": "<PERSON><PERSON><PERSON><PERSON> betalinger", "reports.financialSummary": "Finansiell oppsummering", "reports.netSales": "Netto salg", "reports.orderList.captured": "<PERSON>lt", "reports.orderList.customer": "Kunde", "reports.orderList.id": "Ordre ID", "reports.orderList.netSales": "Netto salg", "reports.orderList.paymentMethod": "Betalingsmetode", "reports.orderList.totalSales": "<PERSON><PERSON><PERSON> salg", "reports.orderList.accountingStatus": "Regnskapsstatus", "reports.orderList.vat": "MVA", "reports.refunds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reports.sales": "<PERSON><PERSON>", "reports.salesList": "<PERSON><PERSON>", "reports.salesReport": "Salgsrapport (PDF)", "reports.totalSales": "Totalt salg", "reports.transactionReportPdf": "Transaksjonsrapport (PDF)", "reports.transactionReportXlsx": "Transaksjonsrapport (XLSX)", "reports.vat": "MVA", "reports.employeeReports.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reports.employeeReports.table.title": "Ansatte", "reports.employeeReports.table.execNumSales": "Totalt ordre", "reports.employeeReports.table.execSalesIncVat": "Totalt inkl. mva", "reports.employeeReports.table.execSalesExVat": "Totalt eks. mva", "reports.employeeReports.table.numSent": "<PERSON><PERSON> beta<PERSON>", "reports.employeeReports.table.sentSalesIncVat": "Totalt inkl. mva (Sendt)", "reports.employeeReports.table.sentSalesExVat": "Totalt eks. mva (Sendt)", "reports.employeeReports.table.numSales": "<PERSON>lt<PERSON> ordre", "reports.employeeReports.table.salesIncVat": "Totalt inkl. mva (Betalt)", "reports.employeeReports.table.salesExVat": "Totalt eks. mva (Betalt)", "reports.timeTracking.title": "Time tracking for employees", "reports.timeTracking.hideToggle": "<PERSON><PERSON><PERSON><PERSON> ansatte uten føringer", "reports.timeTracking.hideUntrackedRows": "<PERSON><PERSON><PERSON><PERSON> rader uten ført tid", "reports.timeTracking.table.employee": "<PERSON><PERSON><PERSON>", "reports.timeTracking.table.totalTime": "Total tid", "reports.timeTracking.table.totalTrackedTime": "Totalt ført tid", "reports.timeTracking.table.totalAssignedTime": "Total oppdragstid", "reports.employeeReports.table.total": "Total", "reports.timeTracking.table.order": "Ordre", "reports.timeTracking.table.description": "Beskrivelse", "reports.timeTracking.table.startStop": "Start - Stopp", "reports.timeTracking.table.trackedTime": "<PERSON><PERSON><PERSON> tid", "reports.timeTracking.table.assignedTime": "Oppdragstid", "reports.timeTracking.table.date": "Da<PERSON>", "reports.timeTracking.noAccountingToolTip": "Den ansatte er ikke koblet til regnskapssystemet", "orders.orderList.orderNumber": "Ordrenummer", "orders.orderList.total_amount_inc_vat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderList.calculated_discount_amount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderList.sales_price": "Totalpris inkl. MVA", "orders.orderList.sales_price_vat_amount": "MVA-beløp", "orders.orderList.sales_price_ex_vat": "Totalpris eks. MVA", "orders.orderList.discount_reason": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.orderList.execution_at": "Utførelsesdato", "orders.orderList.payment_recipient_name": "Betalingsmottakerens navn", "orders.orderList.service_recipient_name": "Tjenestemottakerens navn", "orders.orderList.payment_status_name": "<PERSON>lings<PERSON><PERSON>", "orders.orderList.payment_method_name": "Betalingsmetode", "orders.orderList.main_product_name": "Hovedproduktnavn", "orders.orderList.quote_sent_at": "Til<PERSON>d sendt dato", "orders.orderList.refunded_amount": "Refunder<PERSON> beløp", "orders.orderList.invoice_sent_at": "Faktura sendt dato", "orders.orderList.invoice_send_type_name": "Faktura sendetype", "orders.orderList.invoice_due_date_days": "Fakturaforfallsdager", "orders.orderList.invoice_email": "Faktura e-post", "orders.orderList.invoice_reference_text": "Faktura referanse", "orders.orderList.captured_at": "Betalingsdato", "orders.orderList.created_at": "Oppret<PERSON><PERSON> dato", "orders.orderList.assignedCrew": "Ansatte", "orders.orderList.order_status_name": "<PERSON><PERSON><PERSON><PERSON>", "orders.orderList.export": "Eksporter til Excel", "reports.timeTracking.table.backBtn": "Timeføring", "reports.timeTracking.table.export": "Eksporter", "reports.timeTracking.table.addModal": "Legg til", "reports.timeTracking.modal.editTitle": "<PERSON>iger tidsregistrering", "reports.timeTracking.modal.title": "Legg til tidsregistrering", "reports.timeTracking.modal.duration": "<PERSON><PERSON><PERSON><PERSON>", "reports.timeTracking.modal.date": "Da<PERSON>", "reports.timeTracking.modal.description": "Beskrivelse", "reports.timeTracking.modal.comment": "Kommentar", "reports.timeTracking.modal.useInSalary": "<PERSON>is i timesgodkjenning", "reports.timeTracking.modal.autoRegistered": "Automatisk opprettet", "reports.timeTracking.modal.employee": "<PERSON><PERSON><PERSON>", "reports.timeTracking.modal.selectEmployee": "<PERSON><PERSON><PERSON> an<PERSON>t", "reports.timeTracking.modal.job": "<PERSON><PERSON> (valg<PERSON><PERSON>t)", "reports.timeTracking.modal.activity": "Aktivitet", "reports.timeTracking.modal.project": "Prosjekt (valgfritt)", "reports.timeTracking.modal.department": "Avdeling (valgfritt)", "reports.timeTracking.modal.start": "Start", "reports.timeTracking.modal.stop": "Stopp", "resetPassword.confirmPassword": "Bekreft passord", "resetPassword.confirmPasswordLabel": "Skriv inn passord på nytt", "resetPassword.description": "Skriv inn nytt passord", "resetPassword.password": "Passord", "resetPassword.passwordLabel": "Skriv inn nytt passord", "resetPassword.submitButtonLabel": "Bekreft", "resetPassword.title": "Tilbakestill passord", "resources.EditResource": "", "resources.addNewResource": "Legg til ny ressurs", "resources.addResource": "Legg til ressurs", "resources.chooseResourceType": "Velg ressurstype", "resources.clickHereToUploadImage": "<PERSON><PERSON><PERSON> her for å laste opp bilde", "resources.createdAt": "Opprettet", "resources.editName": "<PERSON><PERSON> ressurs", "resources.editVehicleData": "<PERSON><PERSON>inform<PERSON>", "resources.error.noVehicleFound": "Ingen kjøretøy funnet", "resources.error.specialChar": "Spesialtegn er ikke tillatt", "resources.image": "<PERSON><PERSON>", "resources.itemName": "<PERSON><PERSON><PERSON><PERSON>", "resources.name": "Navn", "resources.namePlaceholder": "Skriv inn navn", "resources.registrationLabel": "<PERSON><PERSON><PERSON> etter kjøretøy med registreringsnummer", "resources.registrationPlaceholder": "Registreringsnummer", "resources.resourceDescription": "Beskrivelse", "resources.resourceDescriptionPlaceholder": "Skriv inn beskrivelse", "resources.resourceTypeName": "Type", "resources.save": "Lagre", "resources.saveVehicleData": "Lagre kjøretøysinformasjon", "resources.search": "<PERSON><PERSON><PERSON>", "resources.searchRegistrationNumber": "Søk etter registreringsnummer", "resources.unknown": "Uspesifisert", "resources.assettype": "Ressurstype", "resources.vehicleDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "response_assets.created": "Opprettet", "response_assets.workOrderDuplicateNoExecution": "<PERSON> må sette utførelsesdato for denne jobben for den kan dupliseres", "response_assets.workOrderDateAndTimeError": "En jobb kan ikke strekke seg over flere dager", "response_assets.image_removed": "<PERSON><PERSON> s<PERSON>t", "response_assets.image_removed_from_report": "Bilde vil ikke vises i digital rapport", "response_assets.image_added_to_report": "Bilde vil vises i digital rapport", "response_assets.image_added_to_pdf": "Bilde vil vises i PDF-rapport", "response_assets.image_removed_from_pdf": "<PERSON>ilde vil ikke vises i PDF-rapport", "response_assets.contractDeclined": "<PERSON><PERSON> a<PERSON>vist ", "response_assets.contractAccepted": "<PERSON><PERSON> er godtatt, og har blitt flyttet til jobblisten din", "response_assets.no_phone": "Brukeren har ikke telefonnummer", "response_assets.no_email": "Brukeren har ikke e-postadresse", "response_assets.invoiceCredited": "Faktura kreditert", "response_assets.cannot_remove_last_company_admin": "Kan ikke fjerne selskapets eneste administrator", "response_assets.product_accounting_vat_mismatch": "Produkt fra regnskap matcher ikke MVA-koden til dette produktet", "response_assets.duplicated_and_navigated": "<PERSON><PERSON>", "response_assets.sms_sent": "SMS sendt", "response_assets.order_confirmation_email_sent": "E-post sendt", "response_assets.nope": "Nope!", "response_assets.saved": "<PERSON>g<PERSON>", "response_assets.sent": "Send<PERSON>", "response_assets.deleted": "<PERSON><PERSON><PERSON>", "response_assets.checklist_space": "Oppgavenavnet må inneholde minst én bokstav.", "response_assets.company_created": "Selskap opprettet", "response_assets.company_updated": "Selskap oppdatert", "response_assets.contactCreated": "Kontakt opprettet", "response_assets.businessAndContactCreated": "Bedrift og kontakt opprettet", "response_assets.contactCreationError": "Kunne ikke opprette kontakt", "response_assets.customerSaved": "Kunde lagret", "response_assets.customer_user_exists": "", "response_assets.different_password": "Passordene er ikke like", "response_assets.duplicate_email": "Denne e-postadressen er allerede registrert på en eksisterende bruker", "response_assets.duplicate_phone": "Dette telefonnummeret er allerede registrert på en eksisterende bruker", "response_assets.duplicate_product_name": "Produktnavnet er allerede i bruk", "response_assets.employee_created": "<PERSON><PERSON><PERSON> ble opprettet", "response_assets.employee_delete_limit": "Minst én ansatt må forbli; du kan ikke slette alle ansatte.", "response_assets.employee_updated": "<PERSON><PERSON><PERSON> oppdate<PERSON>", "response_assets.event_created": "Aktivitet opprettet", "response_assets.event_deleted": "Aktivitet slettet", "response_assets.event_updated": "Aktivitet endret", "response_assets.pdf_generated_successfully": "PDF generert", "response_assets.quote_sent_successfully": "Tilbud sent", "response_assets.sms_name_updated": "Visningsnavn for tekstmeldinger oppdatert", "response_assets.existing_user_email": "E-postadressen er allerede tilknyttet en eksisterende bruker.", "response_assets.existing_organisation_number": "Et selskap med dette organisasjonsnummeret eksisterer allerede.", "response_assets.negative_order_amount": "Ordren kan ikke ha en negativ totalpris når den sendes til betaling.", "response_assets.negative_order_line_amount": "Ordrelinjer kan ikke ha en negativ totalpris når ordren sendes til betaling", "response_assets.expired_token": "Se<PERSON><PERSON><PERSON> har u<PERSON><PERSON><PERSON>, vennligst logg inn på nytt.", "response_assets.global_response_bad_request": "En feil har op<PERSON><PERSON><PERSON>, kontakt support for hjelp.", "response_assets.global_response_created": "New item has been successfully created.", "response_assets.global_response_duplicate_error": "This item already exists", "response_assets.global_response_forbidden": "You do not have permission to access this resource.", "response_assets.global_response_gone": "The requested content is no longer available on the server.", "response_assets.global_response_internal_server_error": "En feil har op<PERSON><PERSON><PERSON>, kontakt support for hjelp.", "response_assets.global_response_method_not_allowed": "The method used in the request is not allowed for the requested URL.", "response_assets.global_response_no_content": "No content available to display.", "response_assets.global_response_payload_too_large": "The request payload is too large. Please reduce the size and try again.", "response_assets.global_response_service_unavailable": "The server is currently unable to handle the request due to temporary overloading or maintenance of the server.", "response_assets.global_response_success": "The operation was successful!", "response_assets.global_response_unauthorized": "Unauthorized access.", "response_assets.global_response_unsupported_media_type": "The server is refusing to service the request because the entity of the request is in a format not supported by the requested resource for the requested method.", "response_assets.global_response_updated": "The item has been successfully updated.", "response_assets.notAvailableInOfflineMode": "Denne operasjonen er ikke tilgjengelig i offline-modus", "response_assets.inputFieldSpacing": "<PERSON><PERSON> inneholde et tegn og kan ikke kun bestå av mellomrom.", "response_assets.invitation_failed": "Invitasjon kunne ikke sendes", "response_assets.invitation_sent": "Invitasjon sendt", "response_assets.logo_size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sørg for at filstørrelsen på logoen ikke overskrider 10 MB.", "response_assets.missing_company_id": "Du har ikke valgt et selskap.", "response_assets.multiple_fixed_percentage": "Kun <PERSON>n '<PERSON> - Prosent' trigger er tillatt", "response_assets.no_address_found": "Fant ingen adresse.", "response_assets.no_addresses_found": "", "response_assets.no_billable": "Minst ett steg må være fakturerbart", "response_assets.order_is_already_paid": "Ordren er allerede betalt", "response_assets.order_locked_order_status": "Ordren kan ikke endres grunnet ordrens status", "response_assets.order_locked_payment_status": "Ordren kan ikke endres grunnet ordrens betalingsstatus", "response_assets.order_not_assigned": "", "response_assets.out_of_stock": "<PERSON><PERSON> antall ikke tilgjengelig på lager.", "response_assets.partner_company_exist": "Bedriftsnavnet finnes allerede", "response_assets.photo_size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sørg for at størrelsen på bildet ikke overskrider 10 MB", "response_assets.product_created": "Produkt opprettet", "response_assets.product_updated": "Produkt oppdatert", "response_assets.resource_created": "Ressurs ble opprettet", "response_assets.resource_updated": "Ressurs oppdatert", "response_assets.taskNameValidation": "Oppgavenavn kan ikke være tomt", "response_assets.test_price_rule_end_time": "Sluttid må være etter starttid", "response_assets.unauthorised_for_core": "Du har ikke tilgang til denne platformen", "response_assets.updated": "Oppdatert", "response_assets.url_does_not_exist": "Den forespurte adressen eksisterer ikke", "response_assets.wrong_password": "Ugyldig telefon eller passord", "response_assets.discount_amount_too_low": "Rabattbeløpet kan ikke være null eller lavere enn null", "response_assets.discount_greater_than_100": "Rabattprosenten kan ikke være høyere enn 100%", "response_assets.copy": "Kopiert!", "response_assets.user_is_customer_at_other_companies": "Kunden er en bruker i et annet selskap og kan ikke redigeres", "response_assets.overlap": "Timeføring overlapper", "response_assets.timeTracking_updated": "Timeføring oppdatert", "response_assets.timeTracking_created": "Time<PERSON><PERSON><PERSON> oppret<PERSON>t", "response_assets.syncSuccessfull": "<PERSON><PERSON><PERSON>", "response_assets.syncFailed": "Synkronisering feilet", "response_assets.colorUpdatedSuccess": "<PERSON><PERSON> opp<PERSON>", "response_assets.eventTitleTagsUpdatedSuccess": "Henselsestitler oppdatert", "response_assets.StartEndDatsNotSameDay": "Start og sluttdato må være på samme dag", "response_assets.no_multi_day_work_orders": "Flerdags ordre støttes ikke i kalenderen, opprett jobber for hver dag", "response_assets.exceedFileSize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> overstiger 10 MB. Vennligst velg en mindre fil.", "response_assets.multiDayWorkOrderDateAndTimeError": "Jobbens sluttidspunkt kan ikke være før starttidspunkt", "response_assets.settings.company.operateExVat.change.success": "Mva-visning har blitt oppdatert", "response_assets.reportinator.element_created": "Element opprettet", "response_assets.reportinator.element_deleted": "Element slettet", "response_assets.reportinator.writer.template.cant_edit_template_element": "Du kan ikke endre/sette dette i en mal", "response_assets.networkError": "Nettverksfeil, din internettforbindelse er ustabil. Dine endringer er ikke lagret.", "salary.approval.title": "<PERSON><PERSON><PERSON><PERSON> timer", "salary.approval.addTimeTracking": "Legg til føring", "salary.approval.approvals.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "salary.approval.list.periods": "Arbeidsperioder", "salary.approval.list.ongoing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "salary.approval.list.review": "Gjennomgå", "salary.approval.list.time": "Tid", "salary.approval.list.total": "Totalt", "salary.approval.list.activity": "Aktivitet", "salary.approval.list.description": "Beskrivelse", "salary.approval.list.comment": "Kommentar", "salary.approval.list.status": "Status", "salary.approval.week": "<PERSON><PERSON>", "salary.approval.approve": "<PERSON><PERSON><PERSON><PERSON>", "salary.approval.finishPeriod": "Merk periode som ferdig", "salary.approvals.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "salary.approvals.transferToAccounting": "Før i regnskap", "salary.approvals.excel": "Excel", "salary.approvals.list.createdAt": "Opprettet", "salary.approvals.list.createdBy": "Opprettet av", "salary.approvals.list.syncedAccounting": "F<PERSON>rt i regnskap", "salary.approvedHours.title": "Månedsoversikt", "salary.approvedHours.period": "Periode", "salary.approvedHours.addForEmployee": "Legg til for ansatt", "salary.approvedHours.employee": "<PERSON><PERSON><PERSON>", "salary.approvedHours.employee.placeholder": "<PERSON><PERSON><PERSON> an<PERSON>t", "salary.approvedHours.createSalaryRun": "<PERSON><PERSON><PERSON><PERSON> lø<PERSON>jø<PERSON>", "salary.approvedHours.createSalaryRun.withoutAccountingCheck": "Opprett uten regnskapssjekk", "salary.approvedHours.createSalaryRun.withoutMonthly": "<PERSON><PERSON><PERSON><PERSON> uten må<PERSON>lø<PERSON>", "salary.approvedHours.noHoursAvailable": "Ingen timer tilgjengelig for lønnskjøring", "salary.approvedHours.activities": "Aktiviteter", "salary.approvedHours.supplements": "<PERSON><PERSON><PERSON>", "salary.approvedHours.activityEmployee": "Ansatt/Aktivitet", "salary.approvedHours.workingHoursModal.create": "<PERSON><PERSON><PERSON>t ny føring", "salary.approvedHours.workingHoursModal.edit": "<PERSON><PERSON>rt tid", "salary.approvedHours.workingHoursModal.type": "Type", "salary.approvedHours.workingHoursModal.activity": "Aktivitet", "salary.approvedHours.workingHoursModal.salaryType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "salary.approvedHours.workingHoursModal.hours": "Timer", "salary.approvedHours.workingHoursModal.quantity": "<PERSON><PERSON><PERSON>", "salary.approvedHours.workingHoursModal.comment": "Kommentar", "salary.approvedHours.workingHoursModal.totalAmount": "Totalt lønnsgrunnlag", "salary.approvedHours.workingHoursModal.trackings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "salary.approvedHours.workingHoursModal.noTrackings": "Ingen timesføringer tilgjengelig", "salary.approvedHours.workingHoursModal.time": "Tid", "salary.approvedHours.workingHoursModal.description": "Beskrivelse", "salary.approvedHours.workingHoursModal.source": "<PERSON><PERSON>", "salary.approvedHours.workingHoursModal.source.jobHours": "Oppdragstid", "salary.approvedHours.workingHoursModal.source.manual": "<PERSON><PERSON><PERSON> av bruker", "salary.approvedHours.workingHoursModal.noSalary": "Den ansatte mangler et aktivt arbeidsforhold", "salary.absence.title": "Ferie og fravær", "salary.absence.modal.title.create": "<PERSON><PERSON><PERSON><PERSON> fravær", "salary.absence.modal.title.edit": "<PERSON><PERSON> fravær", "salary.absence.modal.absenceType": "Fraværstype", "salary.absence.modal.absenceType.placeholder": "Velg fraværstype", "salary.absence.modal.description": "Beskrivelse", "salary.absence.modal.checkConflicts": "Sjekk konflikter", "salary.activeFrom": "Aktiv fra", "salary.activeTo": "Aktiv til", "salary.selectDate": "<PERSON><PERSON>g dato", "salary.absence.conflictModal.title": "har følgende jobber i perioden", "salary.absence.conflictModal.description": "Du kan nå velge å gå inn på hver enkelt jobb og håndtere tildeling av vikarer manuelt, eller så kan du velge en vikar i nedtrekksmenyen under jobblisten.Etter å ha valgt vikar kan du velge om du vil erstatte {{name}} med vikaren i jobbene du har markert, eller om du kun vil legge vikaren til på valgte jobber.", "salary.absence.conflictModal.substitute.placeholder": "Velg en eller flere vikarer", "salary.absence.conflictModal.substitute.add": "<PERSON><PERSON> til vikar", "salary.absence.conflictModal.substitute.add.tooltip": "<PERSON><PERSON><PERSON> du legger til vikarer på jobbene vil den den ansatte fortsatt være tildelt på jobben, men vil bli merket som fraværende.", "salary.absence.conflictModal.substitute.replace": "Bytt ut med vikar", "salary.absence.conflictModal.substitute.replace.tooltip": "<PERSON><PERSON><PERSON> du bytter ut den ansatte med vikarer vil den ansatte bli fjernet fra jobben.", "salary.absence.conflictModal.substitute.remove": "Fjern {{name}} fra valgte jobber", "salary.absence.conflictModal.list.job": "<PERSON><PERSON>", "salary.absence.conflictModal.list.executionDate": "Utførelsesdato", "salary.absence.list.employee": "<PERSON><PERSON><PERSON>", "salary.pause": "Pause", "save": "Lagre", "seconds": "<PERSON><PERSON>nder", "selectorini.disabled": "Deaktivert", "selectorini.noItemsSelected": "Ingen elementer valgt", "selectorini.noEmployeeSelected": "Ingen ansatte valgt for denne ordren", "selectorini.noResourceSelected": "Ingen ressurs valgt for denne ordren", "selectorini.createNewAddress": "Finner du ikke adressen? Op<PERSON>rett ny adresse", "selectorini.searchAddressPlaceholder": "<PERSON><PERSON><PERSON> etter adresse", "settings.company.edit-company.title": "<PERSON><PERSON> se<PERSON>p", "settings.company.address": "<PERSON><PERSON><PERSON>", "settings.company.archiveOnFinished": "<PERSON><PERSON> ordre når den er fullført", "settings.company.autoFinishWorkOrders": "Fullfør påbegynte jobber automatisk ved midnatt", "settings.company.autoFinishAllWorkOrders": "Fullfør påbegynte og ikke-påbegynte jobber automatisk ved midnatt, dersom ordren er bekreftet", "settings.company.splitOrderEvent": "<PERSON> opp flerdags-ordre i flere eventer", "settings.company.splitOrderEvent.tooltip": "Aktiver for å dele opp en ordre som skal utføres over flere dager, i flere eventer. Start -og slutttid for hvert event vil bli satt automatisk basert på selskapets åpningstider.", "settings.company.showEmployeeInitialsOnly": "Vis ansattes initialer i stedet for profilbilde i hurtiglister", "settings.company.showEmployeeInitialsOnly.tooltip": "Aktiver denne for å vise ansattes initialer i stedet for profilbilde i hurtiglister. Dette kan være lurt hvis dine ansatte har profilbilder som ser like ut i mindre formater.", "settings.company.customerCanOnlyAccept": "Bekreft ordre automatisk når kunden aksepterer tilbud i kundeportalen", "settings.company.customerCanOnlyAccept.tooltip": "Aktiver denne for automatisk bekrefte ordre når kunden aksepterer tilbudet i kundeportalen. H<PERSON> denne er deaktivert må du etter kunde-aksept bekrefte ordren manuelt i Core for å fortsette ordreflyten.", "settings.company.manualOrderConfirmationEmail": "<PERSON><PERSON><PERSON> manuell sending av ordre<PERSON>reft<PERSON>e", "settings.company.manualOrderConfirmationEmail.tooltip": "Aktiver for å hindre systemet i å automatisk sende e-post med ordrebekreftelse til kunder når kunden aksepterer en ordre. Med mindre du aksepterer ordren selv, må du manuelt sende ordrebekreftelsen fra ordresiden.", "settings.company.smsName": "Visningsnavn for tekstmeldinger", "settings.company.city": "By", "settings.company.companyInformation": "Firmainformasjon", "settings.company.companyName": "<PERSON><PERSON><PERSON><PERSON>", "settings.company.country": "Land", "settings.company.email": "E-post", "settings.company.error.address": "Ugy<PERSON><PERSON> adresse", "settings.company.error.city": "Ugyldig By", "settings.company.error.companyName": "Ugyldig firmanavn", "settings.company.error.country": "Ugyldig land", "settings.company.error.email": "Ugyldig e-post", "settings.company.error.organisationNumber": "Ugyldig Organisasjonsnummer", "settings.company.error.phone": "Ugyldig telefonnummer", "settings.company.error.postalCode": "Ugyldig postnummer", "settings.company.logo": "Logo", "settings.company.orders": "Ordre", "settings.company.employees": "Ansatte", "settings.company.employees.geo_lock_switch_label": "Bruk geo-lock for innsjekking på oppdrag", "settings.company.employees.geo_lock_range_label": "Maks innsjekkingsavstand fra oppdragsadresse", "settings.company.title": "Selskapsinnstillinger", "settings.company.quantityCalculationResolutions.label": "Tidsjustering for timebaserte tjenester", "settings.company.quantityCalculationResolutions.none": "Ingen oppjustering", "settings.company.quantityCalculationResolutions.15min": "Oppjuster til nærmeste kvarter", "settings.company.quantityCalculationResolutions.30min": "Oppjuster til nærmeste halvtime", "settings.company.quantityCalculationResolutions.60min": "Oppjuster til nærmeste time", "settings.company.communication": "Kommunikasjon", "settings.company.smsNameDescription": "Navnet som vil vises som avsender av tekstmeldinger sendt fra Between. 4 til 11 tegn - Kun bokstaver, tall, mellomrom og bindestreker tillatt.", "settings.company.phoneNumber": "Telefonnummer", "settings.company.postalCode": "Postnummer", "settings.company.pricePresentation.pricePresentationForBusiness": "Vis priser ink MVA for bedriftskunder", "settings.company.pricePresentation.pricePresentationForPrivate": "Vis priser ink MVA for private kunder", "settings.company.pricePresentation.operateExVat": "Vis priser i eks MVA", "settings.company.calendarSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.company.openingHours": "Åpningstider", "settings.company.openingHours.from": "<PERSON>a", "settings.company.openingHours.to": "Til", "settings.company.calendarSettings.timeFrom": "Tid fra", "settings.company.calendarSettings.timeTo": "Tid til", "settings.company.pricePresentation.title": "<PERSON><PERSON><PERSON><PERSON>", "settings.company.registrationNumber": "Organisasjonsnummer", "settings.company.save": "Lagre", "settings.company.saved": "Lagret!", "settings.company.contractor.title": "<PERSON><PERSON><PERSON><PERSON> som underleverandør", "settings.company.contractor.showCalendar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.company.contractor.tooltip": "<PERSON>ne listen viser alle selskaper som har registrert ditt selskap som underleverandør. Hvis du gir tilgang til kalenderen din, vil det gitte selskapet kunne se en anonymisert versjon av kalenderen din.", "settings.company.uploadImage": "<PERSON><PERSON><PERSON> her for å laste opp bilde", "settings.company.editCompanyDetails": "<PERSON><PERSON> se<PERSON>p", "settings.company.companyColor": "Selskapsfarge", "settings.company.operateExVat.change.title": "<PERSON><PERSON> mva-visning", "settings.company.operateExVat.change.warning": "Advarsel: <PERSON><PERSON> vil endre hvordan priser vises i hele systemet", "settings.company.operateExVat.change.description": "Vennligst gjennomgå alle prisene dine etter denne endringen for å sikre at de vises riktig. Denne endringen vil påvirke hvordan priser vises i alle deler av systemet.", "settings.company.quoteValidityDays": "Tilbudets g<PERSON>d", "settings.company.quoteValidityDays.tooltip": "<PERSON><PERSON><PERSON> dager tilbudet er gyldig, etter dette vil tilbudet utløpe og kunde kan ikke lenger akseptere tilbudet.", "settings.integrations.accounting.activationDescription": "Dato for oppstart", "settings.integrations.accounting.activationSelect": "Velg en aktiveringsdato", "settings.integrations.accounting.activationTitle": "Aktiveringsdato", "settings.integrations.accounting.activationDateDescription": "Integrasjonen mot Tripletex starter fra en ønsket dato. Alle transaksjoner etter denne datoen integreres", "settings.integrations.accounting.betweenMethods": "Between betalingsmetoder:", "settings.integrations.accounting.connection": "Forbindelse", "settings.integrations.accounting.connectionContinue": "Fortsett", "settings.integrations.accounting.connectionTest": "Lagre", "settings.integrations.accounting.editHeader": "Rediger Tripletex-integrasjon", "settings.integrations.accounting.editHeader.fiken": "Rediger Fiken-integrasjon", "settings.integrations.accounting.employeeToken": "Employee token", "settings.integrations.accounting.enterYourToken": "Skriv inn nøkkel her", "settings.integrations.accounting.externalPayments": "<PERSON><PERSON><PERSON><PERSON>", "settings.integrations.accounting.finish": "<PERSON><PERSON><PERSON><PERSON>", "settings.integrations.accounting.freePayments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.integrations.accounting.requireManualPush": "Ikke før automatisk i regnskap", "settings.integrations.accounting.usePaymentDate": "Bruk send-til-betaling som fakturadato", "settings.integrations.accounting.integrationError": "<PERSON>e gikk galt, vennligst kontakt support", "settings.integrations.accounting.integrationSuccess": "Integrasjonen ble vellykket satt opp", "settings.integrations.accounting.intergrationIsActive": "Integrasjonen er aktiv", "settings.integrations.accounting.intergrationWasStartedAt": "Integrasjonen ble aktivert kl", "settings.integrations.accounting.linkToTokenInfo": "Klikk her for å finne ut hvordan du genererer en API-nøkkel i Tripletex:", "settings.integrations.accounting.mapMethodsDescription": "Knytt riktig betalingsmetode sammen.", "settings.integrations.accounting.mapMethodsTitle": "Sammenkobling av betalingsmetoder til dine Tripletex betalingsmetoder.", "settings.integrations.accounting.mapMethodsWhy": "<PERSON><PERSON><PERSON>r må jeg gjøre de<PERSON>?", "settings.integrations.accounting.methodBackendError": "<PERSON>e gikk galt, vennligst kontakt support", "settings.integrations.accounting.methodError": "Alle betalingsmetoder må kartlegges for å fortsette", "settings.integrations.accounting.methodMapping": "<PERSON><PERSON> sammen kontoer", "settings.integrations.accounting.methodsContinue": "Fortsett", "settings.integrations.accounting.methodsSave": "Lagre", "settings.integrations.accounting.methodsSuccess": "Betalingsmetoder ble lagret", "settings.integrations.accounting.paymentMethodsContinue": "Fortsett", "settings.integrations.accounting.paymentMethodsCreate": "Opprett betalingsmetoder i Tripletex", "settings.integrations.accounting.paymentMethodsDescription": "Betalingsmetodene som brukes i Between skal nå oprettes i Tripletex.", "settings.integrations.accounting.paymentMethodsTitle": "Betalingsmetoder", "settings.integrations.accounting.paymentMethodsVideo": "Nedenfor er en link til hvordan man oppretter dette i Tripletex. Vi anbe<PERSON><PERSON> at man gjør dette sammen med en regnskapsfører.", "settings.integrations.accounting.postExternalInAccountingTooltip": "Velg om ordre med ekstern betalingshåndtering skal føres i regnskapet. Selve betalingen vil ikke føres, kun ordren.", "settings.integrations.accounting.postFreeInAccountingTooltip": "Velg om gratisordre skal føres i regnskapet. Selve betalingen vil ikke føres, kun ordren.", "settings.integrations.accounting.postInAccounting": "Før ordre i regnskap", "settings.integrations.accounting.postInAccountingTooltip": "", "settings.integrations.accounting.selectDatePlaceholder": "<PERSON><PERSON>g dato", "settings.integrations.accounting.selectMethod": "Velg metode for kartlegging", "settings.integrations.accounting.setUpYourConnection": "Sett opp din forbindelse til Tripletex", "settings.integrations.accounting.startDateUpdated": "Startdato oppdatert", "settings.integrations.accounting.step1": "K<PERSON>ss av for 'Tilpasset oppsett'", "settings.integrations.accounting.step2": "Velg 'Alle tilganger'", "settings.integrations.accounting.step3": "Applikasjonsnavn: Between", "settings.integrations.accounting.step4": "Navn på nøkkelen er: Between", "settings.integrations.accounting.stepGuideHeading": "Under steg 2 punkt 5, g<PERSON><PERSON><PERSON> <PERSON>ø<PERSON>:", "settings.integrations.accounting.stop": "Stopp integrasjonen", "settings.integrations.accounting.stopDescription": "<PERSON><PERSON> du ønsker å deaktivere Tripletex-integrasjonen, bruk knappen nedenfor.", "settings.integrations.accounting.title": "Sett opp din Tripletex-integrasjon", "settings.integrations.accounting.tokenError": "Token er ugyldig", "settings.integrations.accounting.tokenInfo": "En API-nøkkel (employee-token) er en kode du henter ut fra din Tripletex-konto. Denne koden gjør at Between og Tripletex kan snakke sammen.", "settings.integrations.accounting.tokenSuccess": "Token er gyldig", "settings.integrations.accounting.tripletexMethods": "Tripletex betalingsmetoder:", "settings.integrations.accounting.updateDateBtn": "<PERSON><PERSON><PERSON><PERSON> dato", "settings.integrations.powerofficego.accountBackendError": "<PERSON>e gikk galt, vennligst kontakt support", "settings.integrations.powerofficego.accountContinue": "Fortsett", "settings.integrations.powerofficego.accountError": "Alle MVA-satser må kobles til for å fortsette", "settings.integrations.powerofficego.accountMapping": "<PERSON><PERSON> kontoer mot MVA-satser", "settings.integrations.powerofficego.accountMappingDescription": "", "settings.integrations.powerofficego.accountSave": "Lagre", "settings.integrations.powerofficego.accountSuccess": "<PERSON><PERSON><PERSON> lagret", "settings.integrations.powerofficego.activationDescription": "Integrasjonen mot PowerOffice Go starter fra en ønsket dato. Alle transaksjoner etter denne datoen integreres.", "settings.integrations.powerofficego.activationSelect": "Velg en aktiveringsdato", "settings.integrations.powerofficego.activationTitle": "Aktiveringsdato", "settings.integrations.powerofficego.betweenMethods": "Between betalingsmetoder:", "settings.integrations.powerofficego.connection": "Forbindelse", "settings.integrations.powerofficego.connectionContinue": "Fortsett", "settings.integrations.powerofficego.connectionTest": "Lagre", "settings.integrations.powerofficego.editHeader": "Rediger PowerOffice Go-integrasjon", "settings.integrations.powerofficego.employeeToken": "Employee token", "settings.integrations.powerofficego.enterYourToken": "Skriv inn nøkkel her", "settings.integrations.powerofficego.externalPayments": "<PERSON><PERSON><PERSON><PERSON>", "settings.integrations.powerofficego.finish": "<PERSON><PERSON><PERSON><PERSON>", "settings.integrations.powerofficego.integrationError": "<PERSON>e gikk galt, vennligst kontakt support", "settings.integrations.powerofficego.integrationSuccess": "Integrasjonen ble vellykket satt opp", "settings.integrations.powerofficego.intergrationIsActive": "Integrasjonen er aktiv", "settings.integrations.powerofficego.intergrationWasStartedAt": "Integrasjonen ble aktivert kl", "settings.integrations.powerofficego.linkToTokenInfo": "Klikk her for å finne ut hvordan du genererer en API-nøkkel i PowerOffice Go:", "settings.integrations.powerofficego.mapAccountsTitle": "Sammenkobling av MVA-satser til dine PowerOffice Go kontoer.", "settings.integrations.powerofficego.mapMethodsDescription": "Knytt riktig betalingsmetode sammen.", "settings.integrations.powerofficego.mapMethodsTitle": "Sammenkobling av betalingsmetoder til dine PowerOffice Go kontoer.", "settings.integrations.powerofficego.mapMethodsWhy": "<PERSON><PERSON><PERSON>r må jeg gjøre de<PERSON>?", "settings.integrations.powerofficego.methodBackendError": "<PERSON>e gikk galt, vennligst kontakt support", "settings.integrations.powerofficego.methodError": "Alle betalingsmetoder må kartlegges for å fortsette", "settings.integrations.powerofficego.methodMapping": "<PERSON><PERSON> sammen kontoer", "settings.integrations.powerofficego.methodsContinue": "Fortsett", "settings.integrations.powerofficego.methodsSave": "Lagre", "settings.integrations.powerofficego.methodsSuccess": "Betalingsmetoder ble lagret", "settings.integrations.powerofficego.paymentMethodsContinue": "Fortsett", "settings.integrations.powerofficego.paymentMethodsCreate": "Opprett betalingsmetoder i PowerOffice Go", "settings.integrations.powerofficego.paymentMethodsDescription": "Betalingsmetodene du bruker i Between skal nå oprettes som kontoer i PowerOffice Go.", "settings.integrations.powerofficego.paymentMethodsTitle": "Opprett kontoer i PowerOffice Go", "settings.integrations.powerofficego.paymentMethodsVideo": "Nedenfor er en link til hvordan du oppretter dette i PowerOffice Go. Vi anbefaler at du gjør dette sammen med din regnskapsfører.", "settings.integrations.powerofficego.pogoAccounts": "PowerOffice Go kontoer", "settings.integrations.powerofficego.postInAccounting": "Før ordre i regnskap", "settings.integrations.powerofficego.postInAccountingTooltip": "Velg om ordre med ekstern betalingshåndtering skal føres i regnskapet. Selve betalingen vil ikke føres, kun ordren.", "settings.integrations.powerofficego.selectDatePlaceholder": "<PERSON><PERSON>g dato", "settings.integrations.powerofficego.selectMethod": "Velg metode for kartlegging", "settings.integrations.powerofficego.setUpYourConnection": "Sett opp din forbindelse til PowerOffice Go", "settings.integrations.powerofficego.startDateUpdated": "Startdato oppdatert", "settings.integrations.powerofficego.step1": "Klikk legg til utvidelse", "settings.integrations.powerofficego.step2": "Velg 'Between' fra listen", "settings.integrations.powerofficego.step3": "<PERSON><PERSON><PERSON>", "settings.integrations.powerofficego.step4": "Trykk OK", "settings.integrations.powerofficego.stepGuideHeading": "Gjør følgende:", "settings.integrations.powerofficego.stop": "Stopp integrasjonen", "settings.integrations.powerofficego.stopDescription": "Hvis du ønsker å deaktivere PowerOffice Go-integrasjonen, bruk knappen nedenfor.", "settings.integrations.powerofficego.title": "Sett opp din PowerOffice Go-integrasjon", "settings.integrations.powerofficego.tokenError": "<PERSON><PERSON><PERSON><PERSON> er ugyldig", "settings.integrations.powerofficego.tokenInfo": "En API-nøkkel (employee-token) er en kode du henter ut fra din PowerOffice Go-konto. Denne koden gjør at Between og PowerOffice Go kan snakke sammen.", "settings.integrations.powerofficego.tokenSuccess": "<PERSON><PERSON><PERSON><PERSON> er gyldig", "settings.integrations.powerofficego.tripletexMethods": "PowerOffice Go kontoer:", "settings.integrations.powerofficego.updateDateBtn": "<PERSON><PERSON><PERSON><PERSON> dato", "settings.integrations.powerofficego.vatRates": "MVA-satser", "settings.notifications.enabled": "Aktivert", "settings.notifications.message": "Melding", "settings.notifications.enabledCheckbox": "aktivert", "settings.notifications.hours": "Timer", "settings.notifications.notificationSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.notifications.notificationType": "Varseltype", "settings.payment.accountId": "Account ID", "settings.payment.apiKey": "Api Key", "settings.payment.authKey": "Auth Key", "settings.payment.clientId": "Client ID", "settings.payment.clientSecret": "Client Secret", "settings.payment.dinteroPaymentCredentials": "<PERSON><PERSON> beta<PERSON>", "settings.payment.merchantId": "Merchant ID", "settings.payment.paymentSettings": "Innstillinger for betaling", "settings.payment.paymentSettings.general": "<PERSON><PERSON><PERSON> inn<PERSON>ill<PERSON>", "settings.payment.paymentSettings.general.autoSyncOnSendToPayment": "Synkroniser med regnskap automatisk når betaling sendes", "settings.payment.paymentSettings.general.autoSyncOnSendToPayment.tooltip": "<PERSON><PERSON> denne er avslått vil betalingen kun synkroniseres med regnskap når den blir betalt eller sendes som faktura", "settings.payment.quickpayPaymentCredentials": "Quickpay", "settings.payment.rivertyPaymentCredentials": "Riverty", "settings.payment.save": "<PERSON><PERSON><PERSON>", "settings.payment.secretKey": "Secret Key", "settings.payment.sveaPaymentCredentials": "Svea", "settings.payment.external": "Eks<PERSON>t hånd<PERSON>te betalinger", "settings.payment.privateCustomerAvailable": "Kan velges av private kunder", "settings.payment.businessCustomerAvailable": "Kan velges av bedriftskunder", "settings.payment.disabledActiveTooltip": "Du kan ikke aktivere en betalingsmetode før nøklene for betalingsmetoden er lagret og verifisert", "settings.payment.disabledCustomerActivationTooltip": "Vennligst aktiver betalingsmetoden først", "settings.payment.disabledInvoiceActiveTooltip": "Du må sette opp regnskapsintegrasjon før du kan aktivere faktura som betalingsmetode", "settings.payment.privateCustomerActivationTooltip": "Ved å aktivere denne funksjonen vil private kunder kunne velge denne betalingsmetoden i kundeportalen", "settings.payment.businessCustomerActivationTooltip": "Ved å aktivere denne funksjonen vil bedriftskunder kunne velge denne betalingsmetoden i kundeportalen", "settings.payment.autoSendInvoice": "Send fakturaer automatisk når ordre sendes til betaling", "settings.payment.markExternalAsPaid": "Marker automatisk ordre med ekstern betalingshåndtering som betalt når de sendes til betaling", "settings.payment.subscriptionAllowedLabel": "Tillat for repeterende betaling av abonnementer", "settings.payment.subscriptionNotAvailable": "<PERSON><PERSON><PERSON> for denne betalingsmetoden", "settings.payment.subscriptionAllowedTooltip": "<PERSON><PERSON> denne er aktiv, vil kundene kunne benytte denne betalingsmetoden for å autorisere betaling av fremtidige ordreserie-ordre", "settings.payment.dueDateBusiness": "Standard forfallsdato for bedriftskunder", "settings.payment.dueDateBusiness.tooltip": "Sett standard forfallsdato for nye bedriftskunder. Forfallsdatoen for hver kunde kan også settes individuelt i kundeinnstillingene", "settings.payment.dueDatePrivate": "Standard forfallsdato for private kunder", "settings.payment.dueDatePrivate.tooltip": "Sett standard forfallsdato for nye private kunder. Forfallsdatoen for hver kunde kan også settes individuelt i kundeinnstillingene", "settings.billing.title": "Fakturering", "settings.billing.paymentMethod": "Betalingsmetode", "settings.billing.cardNumber": "Kortnummer", "settings.billing.cardNumberRequired": "Kortnummer er påkrevd", "settings.billing.cardNumberInvalid": "Vennligst oppgi et gyldig 16-sifret kortnummer", "settings.billing.cardholderName": "Kortholders navn", "settings.billing.cardholderNameRequired": "Kortholders navn er påkrevd", "settings.billing.expiryMonth": "Utløpsmåned", "settings.billing.expiryMonthRequired": "Utløpsmåned er påkrevd", "settings.billing.expiryYear": "U<PERSON><PERSON>ps<PERSON>r", "settings.billing.expiryYearRequired": "Utløpsår er påkrevd", "settings.billing.cvv": "CVV", "settings.billing.cvvRequired": "CVV er påkrevd", "settings.billing.saveCard": "Lagre kort", "settings.billing.addCard": "Legg til betalingsmetode", "settings.billing.changeCard": "<PERSON><PERSON>", "settings.billing.tryAgain": "<PERSON><PERSON><PERSON><PERSON> ig<PERSON>n", "settings.billing.noCardMessage": "Du har ingen betalingsmetode registrert. Vennligst legg til et kredittkort for å fortsette.", "settings.billing.noMainPayment": "Abonnementsavtalen for ditt selskap er ikke opprettet enda.", "settings.billing.redirectingMessage": "Omdirigerer til sikker betalingsside...", "settings.billing.doNotCloseWindow": "Vennligst ikke lukk dette vinduet.", "settings.billing.cardAddedSuccess": "<PERSON>rt er nå lagt til", "settings.billing.paymentProviderError": "Det oppstod en feil ved behandling av betalingsmetoden. Vennligst prøv igjen.", "settings.billing.genericError": "Det oppstod en feil. Vennligst prøv igjen.", "settings.billing.verifyingPayment": "Verifiserer betaling", "settings.billing.verifyingPaymentMessage": "Vi verifiserer betalingen din. Dette kan ta noen øyeblikk.", "settings.billing.paymentVerificationTimeout": "Tidsavbrudd ved betalingsverifisering. Vennligst prøv igjen.", "settings.billing.paymentVerificationError": "Det oppstod en feil ved verifisering av betalingen. Vennligst prøv igjen.", "settings.billing.active": "Aktiv", "settings.billing.expires": "<PERSON><PERSON><PERSON><PERSON>", "settings.billing.invoices": "<PERSON><PERSON><PERSON><PERSON>", "settings.billing.noInvoices": "Ingen fakturaer tilgjengelig", "settings.billing.invoiceId": "Fakturanummer", "settings.billing.date": "Da<PERSON>", "settings.billing.amount": "<PERSON><PERSON><PERSON>", "settings.billing.status": "Status", "settings.billing.actions": "<PERSON><PERSON>", "settings.billing.download": "Last ned", "settings.resources.addNewResource": "Legg til ny ressurs", "settings.resources.closeTimeLabel": "Stengetid:", "settings.resources.friday": "Fred<PERSON>", "settings.resources.monday": "Mandag", "settings.resources.openTimeLabel": "Åpningstid:", "settings.resources.orderLimitPerDayLabel": "Bestillingsgrense per dag", "settings.resources.orderWarningLimitLabel": "<PERSON><PERSON><PERSON> for bestillingsgrense", "settings.resources.resourceFormTitle": "Ressursinnstillinger:", "settings.resources.resourceSettings": "Ressursinnstillinger", "settings.resources.saturday": "<PERSON><PERSON><PERSON><PERSON>", "settings.resources.save": "Lagre", "settings.resources.sunday": "<PERSON><PERSON><PERSON>g", "settings.resources.thursday": "Torsdag", "settings.resources.tuesday": "Tirsdag", "settings.resources.wednesday": "Onsdag", "settings.calculations.title": "Ka<PERSON>uleringsinnstillinger", "settings.calculations.includeTransportInCalculationLabel": "Inkluder transporttid i automatisk utregning av ordrevarighet", "settings.calculations.includeTransportInCalculationTooltip": "<PERSON><PERSON> denne er aktiv vil tiden det tar å kjøre runden fra hovedkontor, innom hver adresse i ordren, og tilbake igjen, legges til i ordrens varighet ved automatisk kalkulering. Tiden beregnes av Google Maps", "settings.calculations.defaultValuesCardTitle": "Standardverdier for ", "settings.calculations.useIfUnspecifiedPropertyType": "Bruk disse verdiene hvis boligtypen for en ordre er ukjent", "settings.calculations.livableArea": "Kvadratmeter", "settings.calculations.livableArea.suffix": "kvm", "settings.calculations.room.suffix": "stk", "settings.calculations.rooms": "<PERSON>tal<PERSON> rom", "settings.calculations.bathrooms": "Antal<PERSON> bad", "settings.calculations.floor": "<PERSON><PERSON><PERSON>", "settings.calculations.floor.suffix": "etg", "settings.calculations.numberOfFloors": "<PERSON><PERSON><PERSON>", "settings.calculations.shedArea": "<PERSON><PERSON><PERSON><PERSON> på bod", "settings.calendar.pageTitle": "<PERSON><PERSON><PERSON>innst<PERSON><PERSON>", "settings.calendar.selectFieldTitle": "<PERSON><PERSON><PERSON> felt for titel i kalender", "settings.calendar.noFieldSelected": "<PERSON><PERSON> felt valgt", "settings.calendar.colorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.calendar.colorConfiguration": "Fargekonfigurasjon", "settings.calendar.eventTypeColor": "Hendelsestype", "settings.calendar.jobStatusColors": "<PERSON><PERSON><PERSON><PERSON> farge", "settings.calendar.workTemplateColors": "<PERSON><PERSON>-mal farge", "settings.calendar.noWorkOrders": "Ingen jobb-<PERSON>r <PERSON>", "settings.calendar.eventTitleConfiguration": "Tittelkonfigurasjon", "settings.calendar.titleSetup": "<PERSON><PERSON><PERSON> h<PERSON> felt som skal brukes som tittel for hendelsen i kalenderen. Feltene vises i den rekkefølgen de er valgt.", "settings.calendar.availableVariables": "Tilgjengelige variabler", "settings.calendar.selectedVariables": "<PERSON><PERSON><PERSON> variabler", "settings.calendar.noSelectedFields": "<PERSON><PERSON> felt valgt", "settings.calendar.saveEventTitle": "Lagre", "settings.calendar.disable": "<PERSON><PERSON><PERSON><PERSON>", "settings.calendar.enable": "Aktiver", "settings.salary.general": "<PERSON><PERSON><PERSON> inn<PERSON>ill<PERSON>", "settings.salary.standardJobActivity": "Standard aktivitet for jobber", "settings.salary.standardTransportActivity": "Standard aktivitet for transport til og fra jobber", "settings.salary.defaultFixedSalary": "Standard lønnsart for fastlønn", "settings.salary.defaultVacationActivity": "Aktivitet for ferie", "settings.salary.defaultVacationActivity.tooltip": "<PERSON><PERSON><PERSON> denne aktiviteten brukes for fravær, vil varigheten av fraværet bli trukket fra de tilgjengelige feriedagene for den ansatte.", "settings.salary.convertTrackedTimeToWorkingHours": "<PERSON><PERSON><PERSON> ført tid", "settings.salary.convertTrackedTimeToWorkingHours.tooltip": "<PERSON><PERSON> kan overstyres per jobb.", "settings.salary.enableTimetrackingWarning": "<PERSON><PERSON> tidsregistreringer der det er en forskjell mellom planlagt og faktisk ført tid", "settings.salary.timetrackingWarningPercentage.label": "Prosentandel differanse for markering", "settings.salary.timetrackingWarningPercentage.tooltip": "<PERSON><PERSON> denne settes til f.eks 25% vil en jobb med planlagt tid på 1 time markere alle timesregistreringer som har varighet under 45 minutter eller over 1t og 15 minutter.", "settings.salary.activities": "Aktiviteter", "settings.salary.activities.create": "Opprett aktivitet", "settings.salary.activities.standard": "Standard aktivitet for jobber", "settings.salary.salaryTypes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.salary.salaryTypes.create": "<PERSON><PERSON><PERSON><PERSON> lø<PERSON>", "settings.salary.salaryRules": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings.salary.salaryRules.create": "<PERSON><PERSON><PERSON><PERSON> lønnsregel", "settings.salary.activities.modal.noStandardSalaryType": "Ingen standard lønnsart", "settings.salary.activities.modal.notUsedInSalaryCalculation": "Benyttes ikke til lønnsberegning", "settings.salary.activities.modal.title.create": "Opprett aktivitet", "settings.salary.activities.modal.title.edit": "Rediger aktivitet", "settings.salary.activities.modal.type": "Aktivitetstype", "settings.salary.activities.modal.standardSalaryType": "Standard lønnsart for aktivitet", "settings.salary.activities.modal.activityFromAccounting": "Aktivitet fra regnskap", "settings.salary.activities.modal.color": "Aktivitetsfarge", "settings.salary.activities.modal.standardSalaryTypeRequired": "Denne aktivitetstype krever en standard lønnsart", "settings.salary.salaryRules.modal.title.create": "<PERSON><PERSON><PERSON><PERSON> lønnsregel", "settings.salary.salaryRules.modal.title.edit": "<PERSON><PERSON>", "settings.salary.salaryRules.modal.ruleType": "Regeltype", "settings.salary.salaryRules.modal.numberOfHoursForActivation": "<PERSON>tal<PERSON> timer før regelen aktiveres", "settings.salary.salaryRules.modal.activatedSalaryType": "Lønnsart som skal aktiveres", "settings.salary.salaryRules.modal.activeFrom": "Aktiv fra", "settings.salary.salaryRules.modal.activeTo": "Aktiv til", "settings.salary.salaryRules.modal.periodCrash": "Perioden overlapper med en annen periode for samme regeltype", "settings.salary.salaryTypes.modal.title.create": "<PERSON><PERSON><PERSON><PERSON> lø<PERSON>", "settings.salary.salaryTypes.modal.title.edit": "<PERSON><PERSON> lø<PERSON>t", "settings.salary.salaryTypes.modal.type": "Type lønnsart", "settings.salary.salaryTypes.modal.accounting": "Lønnsart fra regnskap", "calendar.loadingEvents": "Laster hendelser...", "eventStatus.unconfirmed": "Ikke bekreftet", "eventStatus.notStarted": "<PERSON>kke startet", "eventStatus.ongoing": "Pågår", "eventStatus.done": "<PERSON><PERSON><PERSON><PERSON>", "eventTypes.activities": "Aktiviteter", "titleFields.workOrderId": "Jobb ID", "titleFields.workOrderTitle": "<PERSON><PERSON> tittel", "titleFields.customerName": "<PERSON><PERSON><PERSON><PERSON> (Tjenestemottaker)", "titleFields.paymentRecipientName": "<PERSON><PERSON><PERSON><PERSON> (Betalingsmottaker)", "titleFields.orderId": "Ordre ID", "titleFields.address": "<PERSON><PERSON><PERSON>", "superadmin.companies.addNewCompany": "Legg til ny bedrift", "superadmin.companies.address": "<PERSON><PERSON><PERSON>", "superadmin.companies.city": "By", "superadmin.companies.companyDetails": "Bedriftsdetaljer", "superadmin.companies.gotoCompany": "Gå til selskap", "superadmin.companies.companyInformation": "Bedriftsinformasjon", "superadmin.companies.companyName": "Bedriftsnavn", "superadmin.companies.country": "Land", "superadmin.companies.createdAt": "Opprettet den", "superadmin.companies.email": "E-post", "superadmin.companies.invoiceEmail": "E-post for faktura", "superadmin.companies.firstName": "Fornavn", "superadmin.companies.id": "ID", "superadmin.companies.invalidAddress": "Ugy<PERSON><PERSON> adresse", "superadmin.companies.invalidCity": "<PERSON><PERSON><PERSON><PERSON> by", "superadmin.companies.invalidCompanyName": "Ugyldig bedriftsnavn", "superadmin.companies.invalidCountry": "Ugyldig land", "superadmin.companies.invalidEmail": "Ugyldig e-postadresse", "superadmin.companies.invalidFirstName": "Ugyldig fornavn", "superadmin.companies.invalidLastName": "Ugyldig etternavn", "superadmin.companies.invalidLogo": "Ugyldig logo", "superadmin.companies.invalidOrgNumber": "Ugyldig organisasjonsnummer", "superadmin.companies.invalidPhone": "Ugyldig telefonnummer", "superadmin.companies.invalidPostalCode": "Ugyldig postnummer", "superadmin.companies.inviteAdmin": "Inviter ekstra administrator", "superadmin.companies.itemName": "Bedrifter", "superadmin.companies.companyType": "Selskapstype", "superadmin.companies.companyType.all": "Alle", "superadmin.companies.search": "<PERSON><PERSON><PERSON>", "superadmin.companies.lastName": "Etternavn", "superadmin.companies.logo": "Bedriftslogo", "superadmin.companies.name": "Navn", "superadmin.companies.orgNumber": "Organisasjonsnummer", "superadmin.companies.phone": "Telefon", "superadmin.companies.postalCode": "Postnummer", "superadmin.companies.save": "Lagre", "superadmin.companies.userInvitation": "Brukerinvitasjon", "superadmin.itemName": "Super Administrator", "tableFooter.display": "Vis", "tableFooter.goToPage": "Gå til side", "tableFooter.of": "av", "tableFooter.page": "Side", "tableFooter.to": "til", "tableFooter.showing": "Viser", "tableFooter.entries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tableBody.noData": "Ingen data", "top-bar.profile-dropdown.logout": "Logg ut", "top-bar.profile-dropdown.terms": "Vilkår", "top-bar.profile-dropdown.profile": "Vis profil", "verifyPopup.areYouSure": "<PERSON>r du sikker?", "verifyPopup.bodyBold": "Sikker på at du vil fortsette?", "verifyPopup.bodyRegular": "Handlingen kan være irreversibel.", "verifyPopup.yes": "<PERSON>a", "verifyPopup.no": "<PERSON><PERSON>", "imageProcessor.header": "<PERSON><PERSON><PERSON><PERSON><PERSON> bi<PERSON>t", "unknown": "N/A", "yes": "<PERSON>a", "backButton.businessCustomer": "Bedriftskunde", "backButton.privateCustomer": "Privatkunde", "backButton.employee": "Profil", "backButton.resources": "<PERSON><PERSON><PERSON><PERSON>", "pdf-invoice.quotationTitle": "Tilbud", "pdf-invoice.s3ImageAlt": "S3 Bilde", "pdf-invoice.quoteToHeading": "Tilbud til", "pdf-invoice.quotationDetailsHeading": "Detaljer om tilbudet", "pdf-invoice.quotationNumber": "Tilbudsnummer", "pdf-invoice.quotationDate": "Tilbudsdato", "pdf-invoice.executionDate": "Utførelsesdato", "pdf-invoice.salesPerson": "<PERSON><PERSON><PERSON>", "pdf-invoice.deliveryAddress": "Levering<PERSON>resse:", "pdf-invoice.seller-title": "Har du noen spørs<PERSON>l? Kontakt meg!", "pdf-invoice.phone": "Telefon", "pdf-invoice.email": "E-post", "pdf-invoice.productName": "Produktnavn", "pdf-invoice.qty": "<PERSON><PERSON><PERSON>", "pdf-invoice.unitPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pdf-invoice.amount": "<PERSON><PERSON><PERSON>", "pdf-invoice.totalExVAT": "Delsum", "pdf-invoice.discount": "<PERSON><PERSON><PERSON>", "pdf-invoice.vat": "Herav MVA", "pdf-invoice.total": "Totalt (ink. MVA)", "pdf-invoice.totalEx": "Totalt (eks. MVA)", "pdf-invoice.importantInformation": "Viktig informasjon", "pdf-invoice.generated-by": "Denne pdfen er generert av Between. www.between.as", "pdf-invoice.filename": "Tilbud til {{name}} #{{orderNumber}}", "pdf-invoice.fixedPayments": "Repterende fast betaling", "pdf-invoice.workOrderPayments": "Repeterende betaling per jobb", "pdf-invoice.singlePayment": "Enkeltbetaling", "pdf-invoice.willBeSent": "<PERSON>il bli sendt", "pdf-workOrder.filename": "Jobb til {{name}} #{{orderNumber}}", "workorder.title": "<PERSON><PERSON>", "workorder.toSection.heading": "<PERSON><PERSON> <PERSON>", "workorder.detailsHeading": "Detaljer om jobben", "workorder.arrivalTime": "Amkomst tid", "workorder.crewTitle": "Mannskap", "workorder.resourceTitle": "<PERSON><PERSON><PERSON><PERSON>", "workorder.orderComment": "Fakturakommentar:", "workorder.notesTitle": "Notater", "workOrder.spesifications.heading": "Kundespørreskjema", "phoneInput.invalidPhone": "Ugyldig telefonnummer", "phoneInput.placeholder": "Skriver inn telefonnummer", "phoneInput.required": "Telefonnummer er påkrevd", "phoneInput.favorites": "<PERSON>st brukte", "embedSettings.list.header": "Bestillingsskjema", "embedSettings.list.subheader": "Bestillingsskjemaer", "embedSettings.list.description": "Opprett tilpasset booking-side der kundene kan bestille eller etterspørre tjenestene dine. Du kan ha flere booking-sider for forskjellige formål", "embedSettings.list.editButton": "<PERSON><PERSON>", "embedSettings.list.createButton": "Nytt skjema", "embedSettings.name.label": "Navn", "embedSettings.name.placeholder": "Bestillingsskjema", "embedSettings.generateBtn": "<PERSON>rer kode", "embedSettings.preview": "Forhåndsvisning", "embedSettings.codeGenerator.header": "<PERSON>rer kode", "embedSettings.codeGenerator.platformSupport.header": "Plattformstøtte", "embedSettings.codeGenerator.platformSupport.description": "", "embedSettings.codeGenerator.embedCode.header": "<PERSON><PERSON>", "embedSettings.codeGenerator.embedCode.description": "Kopier og lim inn koden i din nettsides HTML. Knappen for bestillingsskjema vil vises der du plasserer koden.", "embedSettings.codeGenerator.copyCode": "<PERSON><PERSON><PERSON> te<PERSON>t", "embedSettings.codeGenerator.doneBtn": "Lukk", "embedSettings.preview.header": "Forhåndsvisning", "embedSettings.appearance.header": "Utseende", "embedSettings.appearance.seller": "Kontakt for ordre fra bestillingsskjema", "embedSettings.appearance.primaryColor.label": "P<PERSON>æ<PERSON>arge", "embedSettings.appearance.primaryColor.description": "Farge for bak<PERSON>runn, knapper, og ikoner", "embedSettings.appearance.buttonColor.label": "<PERSON><PERSON> på knapp", "embedSettings.appearance.buttonColor.description": "Tilpass fargene på bestillingsknappen", "embedSettings.appearance.buttonColor.backgroundColor": "Bakgrunnsfarge", "embedSettings.appearance.buttonColor.borderColor": "<PERSON><PERSON><PERSON><PERSON>", "embedSettings.appearance.buttonColor.hoverBackgroundColor": "Bakgrunnsfarge ved hovering", "embedSettings.appearance.buttonColor.hoverBorderColor": "<PERSON><PERSON><PERSON><PERSON> ved hovering", "embedSettings.appearance.buttonColor.textColor": "Tekstfarge", "embedSettings.appearance.buttonColor.textHoverColor": "Tektfarge ved hovering", "embedSettings.appearance.borderRadius.label": "<PERSON><PERSON><PERSON><PERSON> hjørneavrunding", "embedSettings.appearance.borderRadius.description": "Tilpass hjørneavrundingen på bestillingsknappen", "embedSettings.appearance.ButtonText.label": "Tekst på bestillingsknapp", "embedSettings.appearance.ButtonText.description": "Tilpass teksten på bestillingsknappen", "embedSettings.services.header": "T<PERSON>nes<PERSON>", "embedSettings.services.productSearch.label": "Velg tjenester du ønsker å vise i bestillingskjemaet", "embedSettings.services.addQuestionsBtn": "+ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "embedSettings.services.selectedProducts.label": "T<PERSON>nes<PERSON>", "embedSettings.services.questions.header": "<PERSON><PERSON> s<PERSON><PERSON>", "embedSettings.services.editQuestionsBtn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "embedSettings.services.allowRecurring": "Tillat gjentakende bestillinger", "embedSettings.services.modal.title": "Innst<PERSON><PERSON> for", "embedSettings.services.modal.customerBtn": "Spørreskjema", "embedSettings.services.modal.recurringBtn": "Repeterende ordre", "embedSettings.services.modal.recurring.schedule": "<PERSON>legg denne tjenesten som en gjentakende booking?", "embedSettings.services.modal.recurring.scheduletxt": "La kunder planlegge denne tjenesten som en gjentakende booking ved å legge til gjentakende intervaller som kundene vil kunne velge fra. Du kan ogs<PERSON> tilby rabatter for visse intervaller", "embedSettings.config.header": "Innstillinger", "embedSettings.config.showPrices.label": "Vis priser for kunden", "embedSettings.config.showPrices.tooltip": "Vis priser for alle produkter for kunden. Dersom denne er avskrudd vises ikke priser.", "embedSettings.config.Otb.label": "Krev signatur fra kunde", "embedSettings.config.Otb.tooltip": "Ved å ha signatur aktivert må kunden signere med en PIN-kode (OTP) for å bekrefte henvendelsen.", "embedSettings.config.upsell.label": "<PERSON>is mersal<PERSON>produkter", "embedSettings.config.upsell.tooltip": "Aktiveres dersom man ønsker at kunden kan velge valgte mersalgsprodukter i tillegg til hovedprodukt.", "embedSettings.config.exVAT.label": "Vis priser eksklusive MVA.", "embedSettings.config.exVAT.tooltip": "Vis priser eksklusive MVA for kunden. Dersom denne er avskrudd vises priser inkludert MVA.", "embedSettings.marketing.header": "Markedsføring", "embedSettings.marketing.ga.header": "Google Analytics", "embedSettings.marketing.ga.googleTrackingId.label": "Sporings id", "embedSettings.marketing.ga.googleTrackingId.placeholder": "Google sporings id", "embedSettings.marketing.ga.googleConversionSendTo.label": "konvertering 'send til'", "embedSettings.marketing.ga.googleConversionSendTo.placeholder": "Google konvertering 'send til'", "embedModal.chooseProduct.header": "Velg ønsket tjeneste", "embedModal.chooseProduct.startsFrom": "Starter fra", "embedModal.addresses.header": "<PERSON><PERSON><PERSON>", "embedModal.addresses.addNew": "+ Legg til ny", "embedModal.addresses.address": "adresse", "embedModal.addresses.quantity": "<PERSON><PERSON> mange enheter for", "embedModal.addresses.addressLabelPrefix": "<PERSON><PERSON><PERSON>", "embedModal.addresses.addressFieldPlaceholder": "Søk...", "embedModal.addresses.sectionLabel": "Seksjonsnummer", "embedModal.addresses.sectionPlaceholder": "Velg seksjon", "embedModal.addresses.sectionDetails.type": "Type", "embedModal.addresses.sectionDetails.bedrooms": "Soverom", "embedModal.addresses.sectionDetails.garage": "gara<PERSON>je", "embedModal.addresses.sectionDetails.totalRooms": "Totalt antall rom", "embedModal.addresses.sectionDetails.size": "<PERSON><PERSON><PERSON><PERSON>", "embedModal.addresses.bathrooms": "Bad", "embedModal.addresses.sectionDetails.elevator": "<PERSON><PERSON>", "embedModal.addresses.sectionDetails.floors": "<PERSON><PERSON><PERSON>", "embedModal.addresses.sectionDetails.yes": "<PERSON>a", "embedModal.addresses.sectionDetails.no": "<PERSON><PERSON>", "embedModal.addresses.sectionDetails.unknown": "<PERSON><PERSON><PERSON><PERSON>", "embedModal.upsell.headerServices": "Ønsker du å legge til andre tjenester?", "embedModal.upsell.headerStandard": "Ønsker du å legge til andre produkter?", "embedModal.upsell.addBtn": "Legg til", "embedModal.upsell.removeBtn": "<PERSON><PERSON><PERSON>", "embedModal.upsell.quantityTimeUsedNotice": "<PERSON><PERSON><PERSON> vil bli bestemt av tidsbruk", "embedModal.upsell.inventoryReachedNotice": "<PERSON><PERSON> n<PERSON>", "embedModal.dateAndTime.header": "Dato & Tid", "embedModal.dateAndTime.description": "Velg en dato og tid for flyttingen din, og vi vil være der", "embedModal.dateAndTime.availableMessage": "<PERSON><PERSON> datoen din ikke er tilgjenge<PERSON>g, vennligst kontakt oss på ", "embedModal.dateAndTime.or": "eller", "embedModal.dateAndTime.fullDateTooltip": "Vi kan være fullbooket denne dagen, hvis det er tilfelle vil vi kontakte deg etter at du har fullført bookingen", "embedModal.specifications.requiredDescription": "<PERSON><PERSON> spø<PERSON>målet er nødvendig for å fortsette", "embedModal.instructions.header": "Spesielle notater eller instruksjoner", "embedModal.instructions.description": "Skriv inn notatene dine her", "embedModal.customerInformation.header": "Kundeinformasjon", "embedModal.customerInformation.firstName": "Fornavn", "embedModal.customerInformation.firstNamePlaceholder": "Skriv inn ditt fornavn", "embedModal.customerInformation.lastName": "Etternavn", "embedModal.customerInformation.lastNamePlaceholder": "Skriv inn ditt etternavn", "embedModal.customerInformation.email": "E-post", "embedModal.customerInformation.emailPlaceholder": "Skriv inn din e-post", "embedModal.customerInformation.phone": "Telefon", "embedModal.customerInformation.phonePlaceholder": "Skriv inn ditt telefonnummer", "embedModal.confirmation.header": "Bekreft & gjennomgå", "embedModal.confirmation.thankYou": "Takk for din booking", "embedModal.confirmation.buttonFinish": "Gå til ordre i kundeportalen", "embedModal.confirmation.buttonConfirm": "Bekreft booking", "embedModal.confirmation.getOtp": "Bekreft PIN-kode", "embedModal.confirmation.otpHeader": "Skriv inn PIN-koden fra SMS.", "embedModal.confirmation.invalidOtp": "Ugyldig PIN-kode", "embedModal.confirmation.confirmWithoutOTP": "Bekreft", "embedModal.confirmation.didntReceiveSMS": "Fikk du ikke en sms?", "embedModal.confirmation.sendSMSAgain": "Send sms igjen", "embedModal.confirmation.otpSentPleaseTryAgain": "<PERSON><PERSON> sendt, vennligst prøv igjen om", "embedModal.confirmation.seconds": "<PERSON><PERSON>nder", "embedModal.confirmation.termsOfSale": "Salgsvilkår", "embedModal.confirmation.confirmTerms": "<PERSON><PERSON> <PERSON> bekrefte aksepterer du ", "embedModal.confirmation.finished.headerOtp": "Takk for din bestilling!", "embedModal.confirmation.finished.header": "Takk for din forespørsel!", "embedModal.confirmation.finished.bodyOtp": "<PERSON>i har og<PERSON><PERSON> sendt deg en bekreftelse på e-post. Du kan alltid se din ordre i kundeportalen.", "embedModal.confirmation.finished.body": "Vi vil kontakte deg så snart som mulig.", "embedModal.confirmation.finished.contactOtp": "For and<PERSON>ø<PERSON>, kontakt oss på", "embedModal.confirmation.finished.close": "Lukk", "embedModal.summary.header": "<PERSON><PERSON><PERSON><PERSON>", "embedModal.summary.price": "<PERSON><PERSON>", "embedModal.summary.openToSeePrice": "<PERSON><PERSON><PERSON> for å se pris", "embedModal.summary.name": "Navn", "embedModal.summary.phone": "Telefon", "embedModal.summary.email": "E-post", "embedModal.summary.address": "<PERSON><PERSON><PERSON>", "embedModal.summary.date": "Da<PERSON>", "embedModal.summary.hourLabel": "pr time", "embedModal.summary.hourBased": "<PERSON><PERSON> på timer", "embedModal.summary.totalPrice": "Totalpris", "embedModal.summary.calculatingPrice": "<PERSON><PERSON><PERSON><PERSON> pris...", "embedModal.summary.noTotalDescription": "En eller flere produkter er basert på timepris. Totalbeløpet beregnes etter at jobben er utført.", "embedModal.footer.header": "Produktinformasjon", "embedModal.continue": "Fortsett", "embedModal.days.Sunday": "<PERSON><PERSON><PERSON>g", "embedModal.days.Monday": "Mandag", "embedModal.days.Tuesday": "Tirsdag", "embedModal.days.Wednesday": "Onsdag", "embedModal.days.Thursday": "Torsdag", "embedModal.days.Friday": "Fred<PERSON>", "embedModal.days.Saturday": "<PERSON><PERSON><PERSON><PERSON>", "embedModal.months.January": "<PERSON><PERSON><PERSON>", "embedModal.months.February": "<PERSON><PERSON><PERSON>", "embedModal.months.March": "Mars", "embedModal.months.April": "April", "embedModal.months.May": "<PERSON>", "embedModal.months.June": "<PERSON><PERSON>", "embedModal.months.July": "<PERSON><PERSON>", "embedModal.months.August": "August", "embedModal.months.September": "September", "embedModal.months.October": "Oktober", "embedModal.months.November": "November", "embedModal.months.December": "Desember", "reportinator.common.list.name": "Navn", "reportinator.common.list.type": "Type", "reportinator.common.list.updatedBy": "Oppdatert av", "reportinator.common.list.createdAt": "Opprettet", "reportinator.common.list.updatedAt": "Oppdatert", "reportinator.common.createNew.modal.name": "Navn", "reportinator.templates.createNew": "Ny mal", "reportinator.templates.pageName": "Maler", "reportinator.templates.createNew.modal.title": "<PERSON><PERSON><PERSON><PERSON> ny mal", "reportinator.templates.createNew.modal.description": "Beskrivelse", "reportinator.templates.setup": "<PERSON><PERSON><PERSON>", "reportinator.templates.setup.numFloors": "Standard antall etasjer", "reportinator.templates.setup.hasBasement": "Legg til kjeller som standard", "reportinator.templates.setup.hasAttic": "Legg til loft som standard", "reportinator.templates.setup.rooms": "Standard romoppsett", "reportinator.templates.addElement": "Legg til element", "reportinator.templates.addElement.search": "Søk etter element", "reportinator.templates.preqModal.title": "Forutsetninger", "reportinator.reports.list.id": "Rapport-ID", "reportinator.reports.list.customer": "Kunde", "reportinator.reports.list.status": "Status", "reportinator.reports.list.deadlineAt": "Frist for leveranse", "reportinator.reports.list.appraiser": "Taks<PERSON><PERSON>", "reportinator.reports.list.address": "Addresse", "reportinator.reports.createNew": "Ny rapport", "reportinator.reports.pageName": "Rapporter", "reportinator.reports.createNew.modal.title": "<PERSON><PERSON><PERSON><PERSON> ny rapport", "reportinator.reports.createNew.modal.template": "Mal", "reportinator.reports.createNew.modal.templatePlaceholder": "Velg mal", "reportinator.reports.details.writeReport": "Skriv rapport", "reportinator.reports.details.goToOrder": "<PERSON><PERSON> til ordre", "reportinator.reports.details.generalInformation.title": "Generell informasjon", "reportinator.reports.details.generalInformation.serviceRecipient": "Kunde", "reportinator.reports.details.generalInformation.appraiser": "Taks<PERSON><PERSON>", "reportinator.reports.details.generalInformation.present": "Tilstede", "reportinator.reports.details.generalInformation.present.placeholder": "<PERSON><PERSON><PERSON> for å legge til personer", "reportinator.reports.details.generalInformation.present.create": "<PERSON><PERSON><PERSON><PERSON> ny person", "reportinator.reports.details.generalInformation.reference": "Referanse", "reportinator.reports.details.generalInformation.date": "Da<PERSON>", "reportinator.reports.details.generalInformation.time": "Tid", "reportinator.reports.details.generalInformation.deadline": "Leveringsfrist", "reportinator.reports.details.generalInformation.address": "<PERSON><PERSON><PERSON>", "reportinator.reports.details.generalInformation.partner": "Partner", "reportinator.reports.details.generalInformation.comment": "Kommentar", "reportinator.reports.details.propertyInformation.title": "Eiendomsinformasjon", "reportinator.reports.details.propertyInformation.showWaterConnection": "<PERSON>is til<PERSON> vann", "reportinator.reports.details.propertyInformation.hideWaterConnection": "Skjul tilknytning vann", "reportinator.reports.details.propertyInformation.showSewageConnection": "<PERSON>is til<PERSON> avløp", "reportinator.reports.details.propertyInformation.hideSewageConnection": "Skjul tilknytning avløp", "reportinator.reports.details.propertyInformation.showPlotDescription": "<PERSON><PERSON> to<PERSON>", "reportinator.reports.details.propertyInformation.hidePlotDescription": "Skjul tomtebeskrivelse", "reportinator.reports.details.propertyInformation.showLocation": "<PERSON>is beliggenhet", "reportinator.reports.details.propertyInformation.hideLocation": "Skjul beliggenhet", "reportinator.reports.details.propertyInformation.showRegisteredConditions": "Vis tinglyste/andre forhold", "reportinator.reports.details.propertyInformation.hideRegisteredConditions": "<PERSON>k<PERSON><PERSON> tinglyste/andre forhold", "reportinator.reports.details.propertyInformation.showAccessRoad": "<PERSON>is ad<PERSON>tvei", "reportinator.reports.details.propertyInformation.hideAccessRoad": "Skjul adkomstvei", "reportinator.reports.details.propertyInformation.showRegulation": "Vis regulering", "reportinator.reports.details.propertyInformation.hideRegulation": "Skjul regulering", "reportinator.reports.details.propertyInformation.showBuildingDescription": "Vis by<PERSON>krive<PERSON>", "reportinator.reports.details.propertyInformation.hideBuildingDescription": "Skjul bygningsbeskrivelse", "reportinator.reports.details.propertyInformation.waterConnection": "Tilknytning vann", "reportinator.reports.details.propertyInformation.sewageConnection": "Tilknytning avløp", "reportinator.reports.details.propertyInformation.plotDescription": "Beskrivelse av tomten", "reportinator.reports.details.propertyInformation.plotDescription.placeholder": "Gi en kort beskrivelse av tomten", "reportinator.reports.details.propertyInformation.location": "Beliggenhet", "reportinator.reports.details.propertyInformation.location.placeholder": "Beskriv beliggenheten til tomten", "reportinator.reports.details.propertyInformation.tinglysteForhold": "Tingly<PERSON>/andre forhold", "reportinator.reports.details.propertyInformation.tinglysteForhold.placeholder": "<PERSON><PERSON><PERSON>v tinglyste eller andre forhold", "reportinator.reports.details.propertyInformation.accessRoad": "Adkomstvei", "reportinator.reports.details.propertyInformation.accessRoad.placeholder": "Beskriv adkomstveien", "reportinator.reports.details.propertyInformation.regulation": "Regulering", "reportinator.reports.details.propertyInformation.regulation.placeholder": "Beskriv reguleringen", "reportinator.reports.details.propertyInformation.buildingDescription": "Bygningsbeskrivelse", "reportinator.reports.details.propertyInformation.buildingDescription.placeholder": "Beskriv bygningsbeskrivelse", "reportinator.reports.details.value.title": "Verdi", "reportinator.reports.details.value.createValueBtn": "<PERSON><PERSON><PERSON><PERSON> verdiestimat", "reportinator.reports.details.conclusion.title": "Konklusjon", "reportinator.reports.details.propertyInformation.buildingsConclusion": "Konklusjon av bebyggelse", "reportinator.reports.details.propertyInformation.buildingsConclusion.placeholder": "Skriv et sammendrag som beskriver bebyggelsen på tomten.", "reportinator.reports.details.propertyInformation.reportConclusion": "Konklusjon av rapport", "reportinator.reports.details.propertyInformation.reportConclusion.placeholder": "Skriv en konklusjon for rapporten", "reportinator.reports.details.propertyInformation.referenceLevel": "Referansenivå", "reportinator.reports.details.propertyInformation.referenceLevel.placeholder": "Beskriv bygningenes referansenivå", "reportinator.reports.details.areas.elementTitle": "Areal -og romfordeling for", "reportinator.reports.details.upgrades.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reportinator.reports.details.upgrades.year": "<PERSON><PERSON>", "reportinator.reports.details.upgrades.description": "Beskrivelse", "reportinator.reports.details.upgrades.doneBy": "Utført av", "reportinator.reports.details.upgrades.documentation": "Dokumentasjon", "reportinator.reports.details.upgrades.addUpgrade": "Legg til oppgradering", "reportinator.reports.details.upgrades.noUpgrades": "Ingen oppgraderinger er lagt til enda", "reportinator.reports.details.upgrades.modal.createUpgrade": "<PERSON><PERSON><PERSON>t ny oppgradering", "reportinator.reports.details.upgrades.modal.editUpgrade": "<PERSON><PERSON> opp<PERSON>", "reportinator.reports.details.attachments.title": "Kilder og vedlegg", "reportinator.reports.details.attachments.source": "Dokument/Kilde", "reportinator.reports.details.attachments.date": "Da<PERSON>", "reportinator.reports.details.attachments.comment": "Kommentar", "reportinator.reports.details.attachments.status": "Status", "reportinator.reports.details.attachments.modal.createAttachment": "<PERSON><PERSON><PERSON><PERSON> ny kilde", "reportinator.reports.details.attachments.modal.editAttachment": "<PERSON><PERSON> kilde", "reportinator.reports.details.cadastreInformation.title": "<PERSON><PERSON><PERSON>", "reportinator.reports.details.cadastreInformation.kommunenr": "Kommunenr.", "reportinator.reports.details.cadastreInformation.gardsnr": "Gårdsnr.", "reportinator.reports.details.cadastreInformation.bruksnr": "Bruksnr.", "reportinator.reports.details.cadastreInformation.festenr": "Festenr.", "reportinator.reports.details.cadastreInformation.seksjonsnr": "Seksjonsnr.", "reportinator.reports.details.cadastreInformation.leasehold": "Festet", "reportinator.reports.details.cadastreInformation.festekontraktYear": "Festetkontrakt inngått år", "reportinator.reports.details.cadastreInformation.festeAvgiftYear": "Festetavgiften reguleres år", "reportinator.reports.details.cadastreInformation.festekontraktExpirationYear": "Festekontrakt utløper år", "reportinator.reports.details.cadastreInformation.homeowner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reportinator.reports.details.cadastreInformation.businessManager": "Forretningsfører", "reportinator.reports.details.cadastreInformation.shareApartment": "Andelsleilighet", "reportinator.reports.details.cadastreInformation.apartmentNumber": "Leilighetsnr.", "reportinator.reports.details.cadastreInformation.shareNumber": "Andelsnr.", "reportinator.reports.details.cadastreInformation.stockNumber": "Aksjenr.", "reportinator.reports.details.cadastreInformation.ownershipFraction": "Eierbrøk", "reportinator.reports.details.cadastreInformation.housingAssociationName": "Navn på borettslag", "reportinator.reports.details.cadastreInformation.housingAssociationOrgNum": "Org.nr til borettslag", "reportinator.reports.details.cadastreInformation.stockCompanyName": "Navn på aksjeselskap", "reportinator.reports.details.cadastreInformation.stockCompanyOrgNum": "Org.nr til aksjeselskap", "reportinator.reports.details.cadastreInformation.shareOwner": "<PERSON><PERSON><PERSON><PERSON>", "reportinator.reports.details.cadastreInformation.owner": "<PERSON><PERSON>", "reportinator.reports.details.cadastreInformation.ownershipType": "Eierform", "reportinator.reports.details.cadastreInformation.plotArea": "Tomteareal", "reportinator.structure.locationFilter.placeholder": "Filtrer etter lokasjon", "reportinator.structure.addElement": "Legg til element", "reportinator.structure.noElements": "Ingen elementer tilgjengelig", "reportinator.structure.allFloorsFilterItem": "Alle etasjer", "reportinator.structure.editModal.title": "Rediger element", "reportinator.structure.editModal.noParent": "Flytt til toppnivå", "reportinator.structure.editModal.selectParentElement": "Velg hvor du vil flytte elementet til", "reportinator.structure.editModal.subElementsLabel": "<PERSON><PERSON> underelementer for å legge til", "reportinator.structure.editModal.otherElementsLabel": "<PERSON>er elementer for å legge til", "reportinator.structure.editModal.showOtherElements": "<PERSON>is andre elementer", "reportinator.structure.editModal.delete": "<PERSON><PERSON> dette elementet", "reportinator.structure.editModal.noSubElements": "Dette elementet har ingen forhåndsdefinerte underelementer", "reportinator.structure.editModal.noOtherElements": "Dette elementet har ingen andre elementer tilgjenge<PERSON>g", "reportinator.structure.addModal.selectElement": "Velg elementet du ønsker å opprette", "reportinator.structure.addModal.fetchingElements": "Laster inn elementer", "reportinator.structure.addModal.noElementsAvailable": "Ingen elementer tilgjengelig for for gitt plassering", "reportinator.structure.addModal.selectParentElement": "Velg hvor du vil plassere elementet", "reportinator.structure.addModal.noParent": "Legg til på toppnivå", "reportinator.elements.createNew": "Nytt element", "reportinator.elements.pageName": "<PERSON><PERSON><PERSON>", "reportinator.elements.createNew.modal.title": "Opprett nytt element", "reportinator.elements.createNew.modal.useAssessment": "Elementet inneholder tilstandsgrad", "reportinator.elements.showPreview": "Forhåndsvisning", "reportinator.elements.elementType": "Type", "reportinator.elements.name": "Navn", "reportinator.elements.attributes.title": "Attributter", "reportinator.elements.attributes.noAttributes": "Du har ikke lagt til noen attributter enda", "reportinator.elements.attributes.selectorini.search": "Legg til attributt", "reportinator.elements.phrases.title": "<PERSON>", "reportinator.elements.phrases.noPhrases": "Du har ikke lagt til noen fraser enda", "reportinator.elements.phrases.noDataValues": "Ingen attributter tilgjengelig", "reportinator.elements.phrases.titleInput": "<PERSON><PERSON><PERSON>", "reportinator.elements.phrases.textInput": "Tekst", "reportinator.elements.phrases.attribute": "Velg attributt", "reportinator.elements.phrases.addNew": "<PERSON><PERSON><PERSON><PERSON> ny frase", "reportinator.elements.phrases.modal.title": "<PERSON>ase", "newVersionAvailable": "En ny versjon er tilgjengelig", "reportinator.elements.phrases.rules.addNew": "<PERSON><PERSON><PERSON><PERSON> ny regel", "reportinator.elements.phrases.rules.noRules": "Denne frasen har ingen regler enda", "reportinator.elements.phrases.rules.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reportinator.elements.phrases.rules.include": "<PERSON><PERSON><PERSON><PERSON>", "reportinator.elements.phrases.rules.exclude": "Ekskluder", "reportinator.elements.phrases.rules.if": "<PERSON><PERSON><PERSON><PERSON> hvis", "reportinator.elements.subelements.title": "Underelementer", "reportinator.elements.subelements.noSubelements": "Du har ikke lagt til noen underelementer enda", "reportinator.elements.subelements.searchPlaceholder": "Søk etter element", "reportinator.elements.subelements.createAndaddSubelement": "Opprett element og legg til", "reportinator.elements.locationSetup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reportinator.elements.locationSetup.numFloors": "Standard antall etasjer", "reportinator.elements.locationSetup.hasBasement": "Legg til kjeller som standard", "reportinator.elements.locationSetup.hasAttic": "Legg til loft som standard", "reportinator.elements.locationSetup.rooms": "Standard romoppsett", "reportinator.writer.backLabel": "Rapport", "reportinator.writer.scrollToggle": "Scrolling", "reportinator.writer.offline": "Offline", "reportinator.writer.element.helpText.title": "Hjelpetekst", "reportinator.writer.element.instructions.title": "Instruksjoner", "reportinator.writer.element.location.add": "Legg til lokasjon", "reportinator.writer.element.duplicate": "<PERSON><PERSON><PERSON>", "reportinator.writer.element.floorSetup": "<PERSON><PERSON>", "reportinator.writer.element.takePhoto": "<PERSON> bilde", "reportinator.writer.element.gallery": "Galleri", "reportinator.writer.element.description": "Beskrivelse", "reportinator.writer.element.assessment": "Vurdering", "reportinator.writer.element.cause": "Årsak", "reportinator.writer.element.consequence": "Risiki/Konsekvens", "reportinator.writer.element.measure": "Tiltak", "reportinator.writer.element.improvement": "<PERSON><PERSON><PERSON><PERSON>", "reportinator.writer.element.productionYear": "Produksjonsår", "reportinator.writer.element.litres": "Liter", "reportinator.writer.element.material": "Materiale", "reportinator.writer.element.costEstimate": "Kostnadsestimat", "reportinator.writer.element.costEstimateFrom": "Kostnadsestimat fra", "reportinator.writer.element.costEstimateTo": "Kostnadsestimat til", "reportinator.writer.element.imageContainer.title": "Bilder", "reportinator.writer.locationsModal.title": "Legg til lokasjon", "reportinator.writer.locationsModal.floors": "<PERSON><PERSON><PERSON>", "reportinator.writer.locationsModal.rom": "Rom", "reportinator.writer.locationsModal.chooseFloor": "<PERSON><PERSON><PERSON>as<PERSON>", "reportinator.writer.locationsModal.chooseRoom": "Velg rom", "reportinator.writer.editElement.title": "Rediger element", "reportinator.writer.editElement.name": "Elementnavn", "reportinator.writer.editElement.numFloors": "<PERSON>tal<PERSON> etasjer i bygget", "reportinator.writer.editElement.hasBasement": "<PERSON><PERSON><PERSON> har kjeller", "reportinator.writer.editElement.hasAttic": "Bygget har loft", "reportinator.writer.editElement.rooms": "Rom i bygget", "reportinator.writer.editElement.deleteElement": "Slett element", "reportinator.writer.editElement.attributes": "Attributter", "reportinator.writer.areasModal.title": "Romfordeling og arealoppmåling", "reportinator.writer.areasModal.addFloor": "Legg til etasje", "reportinator.writer.areasModal.addRoom": "Rom", "reportinator.writer.areasModal.rooms": "rom", "reportinator.writer.areasModal.roomDistribution": "<PERSON><PERSON><PERSON>eling", "reportinator.writer.areasModal.measurements": "Arealoppmåling", "reportinator.writer.addFloorModal.title": "Legg til etasje", "reportinator.writer.addFloorModal.placeholder": "Velg etasje som skal legges til", "reportinator.writer.addFloorModal.floorName": "Navn på etasje", "reportinator.writer.addFloorModal.floors": "<PERSON><PERSON><PERSON>", "reportinator.writer.editFloorModal.title": "<PERSON><PERSON>", "reportinator.writer.editFloorModal.floorName": "Navn på etasje", "reportinator.writer.editRoomModal.title": "<PERSON><PERSON> rom", "reportinator.writer.editRoomModal.floorName": "Navn på rom", "reportinator.writer.addRoomModal.title": "Legg til rom", "reportinator.writer.addRoomModal.placeholder": "Velg rom som skal legges til", "reportinator.writer.addRoomModal.roomName": "Navn på rom", "reportinator.writer.addRoomModal.rooms": "Rom", "reportinator.writer.duplicateElement.title": "Kopier element", "reportinator.writer.duplicateElement.name": "Navn på nytt element", "reportinator.writer.navigateToPublicPdf": "PDF-visning", "reportinator.writer.offlineModeNavigationWarning": "Endringene du har gjort vil ikke bli lagret før du skrur av offline-modus. <PERSON><PERSON> du forlater siden nå vil du miste alle endringer du har gjort. Ønsker du å forlate siden?", "reportinator.phrases.title": "<PERSON>", "reportinator.phrases.search": "<PERSON><PERSON><PERSON> etter frase", "reportinator.phrases.recommended": "<PERSON><PERSON><PERSON><PERSON><PERSON> fraser", "reportinator.phrases.other": "<PERSON> fraser", "reportinator.phrases.noPhrases": "Ingen fraser lagt til for dette attributtet", "reportinator.elementContainer.ns3940-2012.bra": "BRA", "reportinator.elementContainer.ns3940-2012.pRom": "P-ROM", "reportinator.elementContainer.ns3940-2012.sRom": "S-ROM", "reportinator.elementContainer.ns3940-2012.sum": "Sum", "reportinator.elementContainer.ns3940-2023.bra-i": "BRA-i", "reportinator.elementContainer.ns3940-2023.bra-e": "BRA-e", "reportinator.elementContainer.ns3940-2023.bra-b": "BRA-b", "reportinator.elementContainer.ns3940-2023.openArea": "Åpent areal", "reportinator.elementContainer.ns3940-2023.gua": "GUA", "reportinator.elementContainer.ns3940-2023.sum": "Sum", "reportinator.elementContainer.ns3940-2023.sumBra": "Sum BRA", "reportinator.elementTextField.updated": "Oppdatert", "reportinator.elementTextField.by": "av", "templates.favourite": "<PERSON><PERSON><PERSON><PERSON>", "templates.tasks.overview.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-maler", "templates.tasks.createNew": "<PERSON><PERSON><PERSON><PERSON> mal", "templates.cq.overview.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-maler", "template.cq.back": "Tilbake", "template.cq.createNew": "Legg til", "template.cq.templateName": "Mal navn", "template.cq.noCustomerQuestions": "Ingen spørsmål. Opprett et nytt spørsmål", "template.newTemplate": "Ny mal", "templates.workOrder.overview.title": "Maler for jobber", "templates.workOrder.createNew": "<PERSON><PERSON><PERSON><PERSON> ny mal", "templates.workOrder.edit": "<PERSON><PERSON> mal", "templates.workOrder.modal.name": "Navn på mal", "templates.workOrder.modal.cardTitle": "Tittel og beskrivelse", "templates.workOrder.modal.title": "Tittel for jobb", "templates.workOrder.modal.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> for jobb", "templates.workOrder.modal.addresses": "Tilpass adress<PERSON><PERSON>ett", "templates.workOrder.modal.taskTemplates": "<PERSON><PERSON><PERSON>-maler", "templates.workOrder.modal.taskTemplates.description": "<PERSON><PERSON><PERSON> sjekkliste-maler du ønsker å inkludere i jobb-malen", "templates.workOrder.modal.cqTemplates": "<PERSON><PERSON><PERSON>ø<PERSON>-maler", "templates.workOrder.modal.cqTemplates.description": "<PERSON><PERSON><PERSON> spørreskjema-maler du ønsker å inkludere i jobb-malen", "templates.importantInformation.title": "Maler for viktig informasjon", "templates.importantInformation.createNew": "<PERSON><PERSON><PERSON><PERSON> ny mal", "tablerino.dateRange.next7Days": "Neste 7 dager", "tablerino.dateRange.next30Days": "Neste 30 dager", "tablerino.dateRange.today": "I dag", "tablerino.dateRange.last7Days": "Siste 7 dager", "tablerino.dateRange.last30Days": "Siste 30 dager", "tablerino.dateRange.last90Days": "Siste 90 dager", "tablerino.dateRange.last120Days": "<PERSON>ste 120 dager", "tablerino.dateRange.custom": "Egendefinert", "tablerino.noSavedViews": "Ingen lagrede visninger", "tablerino.savedViews": "<PERSON>g<PERSON><PERSON> v<PERSON><PERSON>er", "tablerino.myViews": "Mine visninger", "tablerino.addView": "+ <PERSON>gg til visning", "tablerino.saveCurrentView": "Lagre visning", "tablerino.updateView": "Opp<PERSON><PERSON> visning", "tablerino.saveView": "Lagre visning", "tablerino.viewName": "Visningsnavn", "tablerino.enterViewName": "Skriv inn visningsnavn", "tablerino.confirmDeleteView": "Er du sikker på at du vil slette denne visningen?", "notifications.header": "<PERSON><PERSON><PERSON>", "notifications.empty": "Ingen nye varsler", "notifications.show_more": "Vis mer", "notifications.customize": "Tilpass varsler", "notifications.internalNote": "<PERSON>tta varsling på interne notater", "Notification.settings": "Varslingsinnstillinger", "notifications.back": "Tilbake", "notifications.finishedNotification": "<PERSON>tta notifikasjon når en jobb fullføres", "notifications.acceptedNotification": "Motta notifikasjon når et tilbud aksepteres", "notifications.upcomingOrderNotAcceptedNotification": "Motta notifikasjon når en ordre nærmer seg og ikke er akseptert", "notifications.rating": "Motta notifikasjon når en kunde gir anmeldelse av ordre", "notifications.embed": "<PERSON>tta notifikasjon om ny ordre fra bestillingsskjema", "notifications.emailFail": "Motta notifikasjon ved feil i e-postutsendelse", "notifications.customerMessage": "<PERSON>tta notifikasjon når en kunde har sendt en melding", "notifications.subcontractorNotification": "<PERSON><PERSON> notifikasjon når du mottar en jobb som underleverandør", "notifications.assignEmployee": "<PERSON><PERSON> notifikasjon når du blir tildelt en jobb", "notifications.customer_cancel_work_order": "<PERSON>tta notifikasjon når kunde avbestiller jobb", "timetracking.registrationtypes.supplement": "<PERSON><PERSON><PERSON>", "timetracking.registrationtypes.activity": "F<PERSON>ring på aktivitet", "notifications.enableNotifications": "For å aktivere varsler: {{instructions}}", "notifications.notificationsBlockedTitle": "<PERSON><PERSON><PERSON> blo<PERSON>", "notifications.notificationSetupFailedTitle": "Varseloppsettet mislyktes", "notifications.notificationSetupFailedBody": "Kan ikke sette opp varsler. Vennligst prøv igjen senere.", "notifications.notificationErrorTitle": "Varsel-feil", "notifications.notificationErrorBody": "Det oppstod en feil under oppsett av varsler. Sjekk nettleserinnstillingene dine.", "notifications.chromeInstruction": "Klikk på ikonet ved siden av adressefeltet → Nettstedinnstillinger → Varsler → Tillat", "notifications.firefoxInstruction": "Klikk på ikonet ved siden av adressefeltet → <PERSON><PERSON><PERSON> → Last inn siden på nytt", "notifications.whereToReceive": "<PERSON><PERSON> vil du motta varsler", "notifications.orderScope": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notifications.ordersAsProjectLeader": "Or<PERSON>r hvor jeg er prosjektleder", "notifications.myOrders": "Bare mine ordrer", "notifications.crewPush": "Push i app", "notifications.corePush": "Push i Core", "notifications.safariInstruction": "Safari → Innstillinger → Nettsteder → Varsler → Finn vårt nettsted og tillat varsler", "notifications.defaultInstruction": "Vennligst sjekk nettleserinnstillingene dine for å aktivere varsler og last inn siden på nytt", "notifications.onlyCreatedOrders": "Kun ordrer bruker har opprettet eller er tildelt", "notifications.onlyAssignedOrders": "<PERSON>n tildelte ordre", "notifications.allOrders": "<PERSON>e ordrer", "settings.navigation.company": "Firma", "settings.navigation.payment": "<PERSON><PERSON>", "settings.navigation.billing": "Fakturering", "settings.navigation.calendar": "<PERSON><PERSON><PERSON>", "settings.navigation.notifications": "<PERSON><PERSON><PERSON>", "settings.navigation.embed": "Bestillingsskjema", "settings.navigation.salary": "<PERSON><PERSON><PERSON>", "settings.navigation.calculations": "<PERSON><PERSON><PERSON><PERSON>", "settings.navigation.resources": "<PERSON><PERSON><PERSON><PERSON>", "settings.navigation.integrations": "Integrasjoner", "settings.navigation.apps": "Apper & Integrasjoner", "settings.navigation.employees": "Ansatte", "settings.navigation.templates": "Maler", "settings.title": "Innstillinger", "settings.billing.paymentHistory": " Betalingshistorikk"}