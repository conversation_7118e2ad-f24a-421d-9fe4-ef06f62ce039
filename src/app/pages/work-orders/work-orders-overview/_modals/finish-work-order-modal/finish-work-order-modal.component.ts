import {Component, Input} from '@angular/core';
import {firstValue<PERSON><PERSON>, forkJoin} from "rxjs";
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {_CRM_ORD_12, _CRM_ORD_31} from "../../../../../@shared/models/input.interfaces";
import {
  QuantityProposalOrderLineResponse,
  WorkOrderResponse,
  WorkOrderCompactResponse
} from "../../../../../@shared/models/order.interfaces";
import {OrderService} from "../../../../../@shared/services/order.service";
import {UtilsService} from "../../../../../@core/utils/utils.service";
import {ToastService} from "../../../../../@core/services/toast.service";
import {StandardImports} from "../../../../../@shared/global_import";

@Component({
  selector: 'app-finish-work-order-modal',
  templateUrl: './finish-work-order-modal.component.html',
  styleUrl: './finish-work-order-modal.component.css',
  standalone: true,
  imports: [StandardImports]
})
export class FinishWorkOrderModalComponent {
  @Input() selectedRows: WorkOrderCompactResponse[] = [];

  processing: boolean = false;
  progress: number = 0;

  constructor(
    public activeModal: NgbActiveModal,
    public utilsService: UtilsService,
    private orderService: OrderService,
    private toastService: ToastService
  ) {}

  ngOnInit() {
    if (!this.selectedRows || this.selectedRows.length === 0) {
      // Handle error or close modal if no work orders were passed.
      console.error('No work orders provided.');
      this.activeModal.close(false);
    }
  }

  // Sequentially finish each work order.
  async finishAllWorkOrders() {
    this.processing = true;
    this.progress = 0;
    const total = this.selectedRows.length;

    // Start a progress simulation that increases the progress gradually.
    const progressInterval = setInterval(() => {
      if (this.progress < 90) { // Only increment until 90%
        this.progress += 10;
      }
    }, 200);

    for (let i = 0; i < total; i++) {
      const workOrderCompact = this.selectedRows[i];

      // Fetch the full work order data to check for order lines with time tracking
      const workOrder: WorkOrderResponse = await firstValueFrom(
        this.orderService.getWorkOrderById(workOrderCompact.work_order_id)
      );

      // Check if this work order has any order lines with time tracking enabled
      const hasTrackedOrderLines = workOrder.order_lines &&
        workOrder.order_lines.some(line => line.track_time);

      // Only process quantity proposals if there are tracked order lines
      if (hasTrackedOrderLines) {
        // Fetch quantity proposals for this work order.
        const proposals: QuantityProposalOrderLineResponse[] = await firstValueFrom(
          this.orderService.getWorkOrderQuantityProposal(workOrder.work_order_id)
        );

        // Build update requests for the proposals.
        const requests = proposals.map((ol) => {
          // Find the specific order line that corresponds to this proposal
          const correspondingOrderLine = workOrder.order_lines
            ? workOrder.order_lines.find(line => line.order_line_id === ol.order_line_id)
            : undefined;

          // Use tracked quantity only if this specific order line has track_time enabled
          const finalQuantity = correspondingOrderLine && correspondingOrderLine.track_time
            ? correspondingOrderLine.quantity
            : ol.quantity;

          // Use order line properties if available, otherwise fall back to proposal properties
          const finalVatRateId = correspondingOrderLine
            ? correspondingOrderLine.vat_rate_id
            : (ol as any).vat_rate_id;
          const finalUnitId = correspondingOrderLine
            ? correspondingOrderLine.unit_id
            : (ol as any).unit_id;

          if (ol.order_line_id === null) {
            const payload: _CRM_ORD_12 = {
              order_id: workOrder.order_id,
              product_id: ol.product_id,
              order_line_name: ol.order_line_name,
              unit_price_inc_vat: ol.precalculated_unit_price_inc_vat,
              vat_rate_id: finalVatRateId,
              unit_id: finalUnitId,
              quantity: finalQuantity,
              price_rule_ids: ol.price_rule_ids,
              comment: ol.comment,
              work_order_id: workOrder.work_order_id,
            };
            return this.orderService.addOrderLineAsCompany(payload);
          } else {
            const payload: _CRM_ORD_31 = {
              order_line_id: ol.order_line_id,
              quantity: finalQuantity
            };
            return this.orderService.updateOrderLineAsCompany(payload);
          }
        });

        // Wait for the update requests (if any) to complete.
        if (requests.length > 0) {
          await firstValueFrom(forkJoin(requests));
        }
      }

      // Always finish the work order, regardless of whether quantity proposals were processed
      await firstValueFrom(this.orderService.finishWorkOrder(workOrder.work_order_id));

      // Update progress after processing each work order.
      this.progress = Math.round(((i + 1) / total) * 100);
    }

    clearInterval(progressInterval);
    // Ensure progress is set to 100% at the end.
    this.progress = 100;
    this.toastService.successToast('updated');
    this.processing = false;
    this.activeModal.close(true);
  }

  cancel() {
    this.activeModal.dismiss();
  }
}
