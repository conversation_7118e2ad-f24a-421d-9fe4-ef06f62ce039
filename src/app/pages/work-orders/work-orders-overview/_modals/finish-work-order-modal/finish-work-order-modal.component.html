<div class="modal-header d-flex justify-content-between align-items-center">
  <h4 class="text-center" style="flex-grow: 1;">{{ "workOrder.finishModal.title" | translate }}</h4>
</div>

<div class="modal-body">
  <p>
    {{ "workOrder.finishModal.confirmationMessage" | translate }}
  </p>
</div>

<div class="modal-footer justify-content-end align-items-center pe-2 gap-2">
  <div *ngIf="processing" class="progress col">
    <div
      class="progress-bar"
      role="progressbar"
      [style.width.%]="progress"
      [attr.aria-valuenow]="progress"
      aria-valuemin="0"
      aria-valuemax="100">
      {{ progress | number:'1.0-0' }}%
    </div>
  </div>
  <app-button
    [translationKey]="'common.cancel'"
    [themeStyle]="'secondary'"
    [disabled]="processing"
    (buttonClick)="cancel()">
  </app-button>
  <app-button
    [translationKey]="'common.yes'"
    [disabled]="processing"
    [loading]="processing"
    (buttonClick)="finishAllWorkOrders()">
  </app-button>
</div>
